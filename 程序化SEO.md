
先了解下什么是程序化 SEO：
> 简单的描述就是通过自动化程序，生成对搜索引擎友好的页面，让搜索引擎尽可能多收录网站页面，让网站内容更容易命中用户搜索需求。

拿 MCP 举例，如何实现程序化 SEO:

1. 先写个爬虫，尽可能多的收集网络上的 MCP Servers，入库的初始数据是 MCP Server 的名称，简介，GitHub 链接等信息

2. 做一遍数据清洗，根据 GitHub 链接拼凑出来 readme 文档地址，用 jina reader 读到内容

3. 根据 readme 的 Markdown 内容，设置提示词，要求 AI 按照固定格式返回一个 JSON，结构化内容可以是这种形式：
- category 自动归类
- tags 自动打标签
- summary 固定格式摘要
 - what is xxx
 - how to use xxx
 - features of xxx
 - use cases of xxx
 - faq from xxx

4. 合理规划网站页面路径，设置面包屑导航。
> 通过网站上清晰的路径规划，让搜索引擎蜘蛛能爬到更多的内容，收录更多的页面
比如：
- 首页导航点击进入分类页
- 分类页点击进入详情页
- 详情页通过面包屑点击回到分类页
- 详情页通过随机推荐进入其他详情页

5. 服务端渲染结构化内容，提升详情页的关键词覆盖密度。比如：
- 详情页的 URL 路径可以是 /server/mcp-server-chatsum
- meta 里面的 title 是 mcp-server-chatsum，description 放 mcp-server-chatsum 的介绍
- H1-H3 标签都包含 chatsum 关键词

6. 为长尾关键词自动构造页面，比如 mcp-server 搜索量不大，但是 xxx-mcp-server 搜索量加起来很大，xxx 可以是一批长尾关键词，就可以定时选一批 xxx-mcp-server 关键词造页面，提高搜索命中率

7. 合理设置 sitemap.xml，可以在 gsc 提交一个主要的 sitemap.xml，在主 sitemap.xml 链接到一个 sitemap_categories.xml 和 sitemap_servers.xml，然后周期性的更新后面两个 xml 的内容，一次性不要提交太多，几十个最好。
