import { Metadata } from 'next'
import { SEOData } from '@/lib/types'

interface SEOProps {
  data: SEOData
  locale: string
}

export function generateSEOMetadata({ data, locale }: SEOProps): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'
  
  return {
    title: data.title,
    description: data.description,
    keywords: data.keywords,
    openGraph: {
      title: data.title,
      description: data.description,
      url: data.canonical || `${baseUrl}/${locale}`,
      siteName: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
      images: [
        {
          url: data.ogImage || `${baseUrl}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: data.title,
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: data.title,
      description: data.description,
      images: [data.ogImage || `${baseUrl}/og-image.jpg`],
    },
    robots: {
      index: !data.noindex,
      follow: !data.noindex,
      googleBot: {
        index: !data.noindex,
        follow: !data.noindex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: data.canonical,
      languages: {
        'zh': `${baseUrl}/zh`,
        'en': `${baseUrl}/en`,
      },
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      yandex: process.env.YANDEX_VERIFICATION,
      yahoo: process.env.YAHOO_VERIFICATION,
    },
  }
}

export function generateStructuredData(data: Record<string, unknown>, type: string, locale: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'
  
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    url: `${baseUrl}/${locale}`,
    name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导'
      : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families',
    inLanguage: locale,
    isPartOf: {
      '@type': 'WebSite',
      name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
      url: baseUrl,
    },
  }

  switch (type) {
    case 'WebSite':
      return {
        ...baseStructuredData,
        '@type': 'WebSite',
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${baseUrl}/${locale}/search?q={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
      }

    case 'FAQPage':
      return {
        ...baseStructuredData,
        '@type': 'FAQPage',
        mainEntity: (data.faqs as Array<Record<string, unknown>>)?.map((faq) => ({
          '@type': 'Question',
          name: faq.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: faq.shortAnswer || (typeof faq.answer === 'string' ? faq.answer.substring(0, 200) + '...' : ''),
          },
        })) || [],
      }

    case 'MedicalWebPage':
      return {
        ...baseStructuredData,
        '@type': 'MedicalWebPage',
        medicalAudience: {
          '@type': 'MedicalAudience',
          audienceType: 'Patient',
        },
        about: {
          '@type': 'MedicalCondition',
          name: 'G6PD Deficiency',
          alternateName: locale === 'zh' ? '蚕豆病' : 'Glucose-6-phosphate dehydrogenase deficiency',
          description: locale === 'zh'
            ? 'G6PD缺乏症是一种遗传性酶缺乏病，患者需要避免某些药物和食物以防止溶血反应'
            : 'G6PD deficiency is a hereditary enzyme deficiency disease where patients need to avoid certain medications and foods to prevent hemolytic reactions',
        },
        lastReviewed: data.lastUpdated || new Date().toISOString(),
        reviewedBy: {
          '@type': 'Organization',
          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',
        },
      }

    case 'Article':
      return {
        ...baseStructuredData,
        '@type': 'Article',
        headline: data.title,
        description: data.description,
        datePublished: data.datePublished || new Date().toISOString(),
        dateModified: data.lastUpdated || new Date().toISOString(),
        author: {
          '@type': 'Organization',
          name: locale === 'zh' ? '医学专家团队' : 'Medical Expert Team',
        },
        publisher: {
          '@type': 'Organization',
          name: locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ',
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/logo.png`,
          },
        },
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': data.canonical || `${baseUrl}/${locale}`,
        },
      }

    case 'BreadcrumbList':
      return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: (data.breadcrumbs as Array<Record<string, unknown>>)?.map((item, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.title,
          item: `${baseUrl}${item.href}`,
        })) || [],
      }

    default:
      return baseStructuredData
  }
}

export function StructuredDataScript({ data, type, locale }: { data: Record<string, unknown>; type: string; locale: string }) {
  const structuredData = generateStructuredData(data, type, locale)
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  )
}

// SEO utility functions
export function generateCanonicalUrl(pathname: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'
  return `${baseUrl}${pathname}`
}

export function generateAlternateUrls(pathname: string): Record<string, string> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd-faq.vercel.app'
  const pathWithoutLocale = pathname.replace(/^\/(zh|en)/, '')
  
  return {
    'zh': `${baseUrl}/zh${pathWithoutLocale}`,
    'en': `${baseUrl}/en${pathWithoutLocale}`,
  }
}

export function generateSEOTitle(title: string, siteName: string, locale: string): string {
  const separator = locale === 'zh' ? ' - ' : ' | '
  return `${title}${separator}${siteName}`
}

export function generateSEODescription(content: string, maxLength: number = 160): string {
  // Remove markdown and HTML
  const cleanContent = content
    .replace(/#{1,6}\s+/g, '')
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/<[^>]*>/g, '')
    .replace(/\n+/g, ' ')
    .trim()
  
  if (cleanContent.length <= maxLength) return cleanContent
  return cleanContent.slice(0, maxLength).replace(/\s+\S*$/, '') + '...'
}
