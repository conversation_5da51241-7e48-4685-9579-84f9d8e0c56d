import Link from 'next/link'

interface FooterProps {
  locale: string
}

export default function Footer({ locale }: FooterProps) {
  const isZh = locale === 'zh'

  const footerLinks = {
    resources: {
      title: isZh ? '资源' : 'Resources',
      links: [
        { name: isZh ? '常见问题' : 'FAQ', href: `/${locale}/faq` },
        { name: isZh ? '用药指导' : 'Medication Guide', href: `/${locale}/medications` },
        { name: isZh ? '症状识别' : 'Symptoms', href: `/${locale}/faq/symptoms` },
        { name: isZh ? '饮食建议' : 'Diet Advice', href: `/${locale}/faq/diet` },
      ],
    },
    medications: {
      title: isZh ? '用药安全' : 'Medication Safety',
      links: [
        { name: isZh ? '中药禁忌' : 'Chinese Medicine', href: `/${locale}/faq/medications/chinese-medicine` },
        { name: isZh ? '口服液安全' : 'Oral Solutions', href: `/${locale}/faq/medications/oral-solutions` },
        { name: isZh ? '西药指导' : 'Western Medicine', href: `/${locale}/faq/medications/western-medicine` },
      ],
    },
    support: {
      title: isZh ? '支持' : 'Support',
      links: [
        { name: isZh ? '搜索' : 'Search', href: `/${locale}/search` },
        { name: isZh ? '关于我们' : 'About Us', href: `/${locale}/about` },
        { name: isZh ? '联系我们' : 'Contact', href: `/${locale}/contact` },
      ],
    },
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">G6</span>
              </div>
              <span className="ml-2 text-xl font-bold">
                {isZh ? 'G6PD指导' : 'G6PD Guide'}
              </span>
            </div>
            <p className="text-gray-400 text-sm">
              {isZh 
                ? '为G6PD缺乏症患者及家属提供权威、全面的医疗信息和生活指导。'
                : 'Providing authoritative and comprehensive medical information and lifestyle guidance for G6PD deficiency patients and families.'
              }
            </p>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key} className="col-span-1">
              <h3 className="text-lg font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm">
              © 2024 G6PD Guide. {isZh ? '保留所有权利。' : 'All rights reserved.'}
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href={`/${locale}/privacy`}
                className="text-gray-400 hover:text-white transition-colors text-sm"
              >
                {isZh ? '隐私政策' : 'Privacy Policy'}
              </Link>
              <Link
                href={`/${locale}/terms`}
                className="text-gray-400 hover:text-white transition-colors text-sm"
              >
                {isZh ? '使用条款' : 'Terms of Service'}
              </Link>
            </div>
          </div>
        </div>

        {/* Medical Disclaimer */}
        <div className="mt-8 p-4 bg-yellow-900 bg-opacity-50 rounded-lg">
          <p className="text-yellow-200 text-sm">
            <strong>{isZh ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
            {isZh 
              ? '本网站提供的信息仅供教育和参考目的，不能替代专业医疗建议、诊断或治疗。如有健康问题，请咨询合格的医疗专业人员。'
              : 'The information provided on this website is for educational and reference purposes only and cannot replace professional medical advice, diagnosis, or treatment. For health issues, please consult qualified medical professionals.'
            }
          </p>
        </div>
      </div>
    </footer>
  )
}
