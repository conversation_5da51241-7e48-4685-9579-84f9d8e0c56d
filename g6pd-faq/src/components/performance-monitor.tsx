'use client'

import { useEffect, useState, useRef } from 'react'
import dynamic from 'next/dynamic'
import Image from 'next/image'

interface PerformanceMetrics {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
}

interface LayoutShift extends PerformanceEntry {
  value: number
  hadRecentInput: boolean
}

interface FirstInputEntry extends PerformanceEntry {
  processingStart: number
}

export function PerformanceMonitor() {
  useEffect(() => {
    // 只在生产环境中启用性能监控
    if (process.env.NODE_ENV !== 'production') return

    const metrics: PerformanceMetrics = {}

    // 监控 Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              metrics.fcp = entry.startTime
            }
            break
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime
            break
          case 'first-input':
            metrics.fid = (entry as FirstInputEntry).processingStart - entry.startTime
            break
          case 'layout-shift':
            if (!(entry as LayoutShift).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as LayoutShift).value
            }
            break
          case 'navigation':
            const navEntry = entry as PerformanceNavigationTiming
            metrics.ttfb = navEntry.responseStart - navEntry.requestStart
            break
        }
      }
    })

    // 观察不同类型的性能指标
    try {
      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift', 'navigation'] })
    } catch (e) {
      // 某些浏览器可能不支持所有指标
      console.warn('Performance monitoring not fully supported:', e)
    }

    // 页面卸载时发送数据
    const sendMetrics = () => {
      if (Object.keys(metrics).length > 0) {
        // 这里可以发送到分析服务
        console.log('Performance Metrics:', metrics)
        
        // 示例：发送到 Google Analytics
        if (typeof (window as unknown as Record<string, unknown>).gtag !== 'undefined') {
          Object.entries(metrics).forEach(([key, value]) => {
            ((window as unknown as Record<string, unknown>).gtag as (...args: unknown[]) => void)('event', 'web_vitals', {
              event_category: 'Performance',
              event_label: key,
              value: Math.round(value),
              non_interaction: true,
            })
          })
        }
      }
    }

    // 页面可见性变化时发送数据
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        sendMetrics()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 页面卸载时发送数据
    window.addEventListener('beforeunload', sendMetrics)

    return () => {
      observer.disconnect()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', sendMetrics)
    }
  }, [])

  return null
}

// 性能优化 Hook
export function usePerformanceOptimization() {
  useEffect(() => {
    // 预加载关键资源
    const preloadCriticalResources = () => {
      const criticalResources = [
        '/fonts/inter-var.woff2',
        '/images/logo.svg',
      ]

      criticalResources.forEach(resource => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.href = resource
        link.as = resource.includes('.woff') ? 'font' : 'image'
        if (resource.includes('.woff')) {
          link.crossOrigin = 'anonymous'
        }
        document.head.appendChild(link)
      })
    }

    // 延迟加载非关键资源
    const lazyLoadNonCriticalResources = () => {
      // 延迟加载分析脚本
      setTimeout(() => {
        if (process.env.NODE_ENV === 'production') {
          // Google Analytics
          const script = document.createElement('script')
          script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`
          script.async = true
          document.head.appendChild(script)

          ;(window as unknown as Record<string, unknown>).dataLayer = (window as unknown as Record<string, unknown>).dataLayer || []
          function gtag(...args: unknown[]) {
            ;((window as unknown as Record<string, unknown>).dataLayer as unknown[]).push(args)
          }
          gtag('js', new Date())
          gtag('config', process.env.NEXT_PUBLIC_GA_ID)
        }
      }, 2000)
    }

    preloadCriticalResources()
    lazyLoadNonCriticalResources()
  }, [])
}

// 图片懒加载组件
export function LazyImage({ 
  src, 
  alt, 
  className, 
  priority = false,
  ...props 
}: {
  src: string
  alt: string
  className?: string
  priority?: boolean
  [key: string]: unknown
}) {
  useEffect(() => {
    if (!priority && 'IntersectionObserver' in window) {
      const images = document.querySelectorAll('img[data-src]')
      
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = img.dataset.src!
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        })
      })

      images.forEach(img => imageObserver.observe(img))

      return () => imageObserver.disconnect()
    }
  }, [priority])

  if (priority) {
    return <Image src={src} alt={alt} className={className} width={100} height={100} {...props} />
  }

  return (
    <Image
      src={src}
      alt={alt}
      className={className}
      loading="lazy"
      width={100}
      height={100}
      {...props}
    />
  )
}

// 代码分割和动态导入工具
export function createDynamicComponent<T = Record<string, unknown>>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  options?: {
    loading?: () => React.ReactElement
    error?: React.ComponentType<{ error: Error }>
  }
) {
  const DynamicComponent = dynamic(importFn, {
    loading: options?.loading || (() => <div>Loading...</div>),
    ssr: false,
  })

  return DynamicComponent
}

// 缓存管理
export class CacheManager {
  private static instance: CacheManager
  private cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>()

  static getInstance() {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  set(key: string, data: unknown, ttl: number = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  clear() {
    this.cache.clear()
  }

  // 清理过期缓存
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// 防抖和节流工具
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastRan = useRef(Date.now())

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}
