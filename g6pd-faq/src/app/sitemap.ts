import { MetadataRoute } from 'next'
import { allFaqData } from '@/lib/faq-data'

export default function sitemap(): MetadataRoute.Sitemap {
  const buildTime = new Date(); // Single timestamp for entire build
  const baseUrl = 'https://g6pd.site'
  const locales = ['zh', 'en']
  
  const staticPages = [
    '',
    '/faq',
    '/faq/medications',
    '/faq/medications/chinese-medicine',
    '/faq/medications/oral-solutions',
    '/faq/symptoms',
    '/faq/diet',
    '/faq/treatment',
    '/search',
    '/medications',
    '/about'
  ]

  const sitemap: MetadataRoute.Sitemap = []

  // Add static pages for each locale
  locales.forEach(locale => {
    staticPages.forEach(page => {
      sitemap.push({
        url: `${baseUrl}/${locale}${page}`,
        lastModified: buildTime,
        changeFrequency: 'weekly',
        priority: page === '' ? 1.0 : page === '/faq' ? 0.9 : 0.8,
      })
    })
  })

  // Add individual FAQ pages (if we create them in the future)
  locales.forEach(locale => {
    const faqs = allFaqData.filter(faq => faq.locale === locale)
    faqs.forEach(faq => {
      sitemap.push({
        url: `${baseUrl}/${locale}/faq/${faq.category}${faq.subcategory ? `/${faq.subcategory}` : ''}#${faq.id}`,
        lastModified: buildTime,
        changeFrequency: 'monthly',
        priority: 0.6,
      })
    })
  })

  return sitemap
}
