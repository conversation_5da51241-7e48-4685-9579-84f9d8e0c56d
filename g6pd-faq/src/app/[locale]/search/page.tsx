'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { searchFaqs, getPopularSearchTerms, getSuggestedQuestions, faqCategories, type FAQItem } from '@/lib/faq-data'

interface SearchPageProps {
  params: Promise<{ locale: string }>
}

function SearchContent({ params }: SearchPageProps) {
  const [locale, setLocale] = useState<'zh' | 'en'>('zh')
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<FAQItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [popularTerms, setPopularTerms] = useState<string[]>([])
  const [suggestions, setSuggestions] = useState<FAQItem[]>([])
  
  const searchParams = useSearchParams()

  const handleSearch = useCallback((searchQuery: string, searchLocale: 'zh' | 'en' = locale, category?: string) => {
    setIsLoading(true)

    setTimeout(() => {
      const searchResults = searchFaqs(searchQuery, searchLocale, category)
      setResults(searchResults)
      setSuggestions(getSuggestedQuestions(searchQuery, searchLocale))
      setIsLoading(false)
    }, 300) // Small delay for better UX
  }, [locale])

  useEffect(() => {
    params.then(({ locale: paramLocale }) => {
      const currentLocale = (paramLocale === 'en' ? 'en' : 'zh') as 'zh' | 'en'
      setLocale(currentLocale)
      setPopularTerms(getPopularSearchTerms(currentLocale))

      // Get initial query from URL params
      const urlQuery = searchParams.get('q') || ''
      if (urlQuery) {
        setQuery(urlQuery)
        handleSearch(urlQuery, currentLocale)
      } else {
        setSuggestions(getSuggestedQuestions('', currentLocale))
      }
    })
  }, [params, searchParams, handleSearch])

  const handleQueryChange = (newQuery: string) => {
    setQuery(newQuery)
    if (newQuery.trim()) {
      handleSearch(newQuery, locale, selectedCategory)
    } else {
      setResults([])
      setSuggestions(getSuggestedQuestions('', locale))
    }
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    if (query.trim()) {
      handleSearch(query, locale, category || undefined)
    }
  }

  const isZh = locale === 'zh'
  const categories = faqCategories[locale]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {isZh ? '搜索FAQ' : 'Search FAQ'}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {isZh 
              ? '搜索G6PD缺乏症相关问题，获取专业的医疗指导和建议'
              : 'Search G6PD deficiency related questions for professional medical guidance and advice'
            }
          </p>
        </div>

        {/* Search Form */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              {/* Search Input */}
              <div className="flex-1">
                <input
                  type="text"
                  value={query}
                  onChange={(e) => handleQueryChange(e.target.value)}
                  placeholder={isZh ? '输入您的问题或关键词...' : 'Enter your question or keywords...'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                />
              </div>
              
              {/* Category Filter */}
              <div className="md:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{isZh ? '所有分类' : 'All Categories'}</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Popular Search Terms */}
            {!query && popularTerms.length > 0 && (
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  {isZh ? '热门搜索：' : 'Popular searches:'}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {popularTerms.map((term, index) => (
                    <button
                      key={index}
                      onClick={() => handleQueryChange(term)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors"
                    >
                      {term}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">{isZh ? '搜索中...' : 'Searching...'}</p>
          </div>
        )}

        {/* Search Results */}
        {!isLoading && query && (
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                {isZh ? '搜索结果' : 'Search Results'}
              </h2>
              <p className="text-gray-600">
                {isZh 
                  ? `找到 ${results.length} 个相关问题`
                  : `Found ${results.length} related questions`
                }
              </p>
            </div>

            {results.length > 0 ? (
              <div className="space-y-4">
                {results.map((faq) => (
                  <div key={faq.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 flex-1">
                        {faq.question}
                      </h3>
                      <span className="ml-4 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full whitespace-nowrap">
                        {categories.find(cat => cat.id === faq.category)?.name}
                      </span>
                    </div>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {faq.answer}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {faq.keywords.slice(0, 3).map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            {keyword}
                          </span>
                        ))}
                      </div>
                      <Link
                        href={`/${locale}/faq/${faq.category}${faq.subcategory ? `/${faq.subcategory}` : ''}#${faq.id}`}
                        className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                      >
                        {isZh ? '查看详情' : 'View Details'} →
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {isZh ? '未找到相关结果' : 'No results found'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {isZh 
                    ? '尝试使用不同的关键词或浏览下面的建议问题'
                    : 'Try different keywords or browse the suggested questions below'
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {/* Suggested Questions */}
        {suggestions.length > 0 && (
          <div className="max-w-4xl mx-auto mt-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              {isZh ? (query ? '相关问题' : '热门问题') : (query ? 'Related Questions' : 'Popular Questions')}
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              {suggestions.map((faq) => (
                <div key={faq.id} className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
                  <h3 className="font-medium text-gray-900 mb-2">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {faq.answer}
                  </p>
                  <Link
                    href={`/${locale}/faq/${faq.category}${faq.subcategory ? `/${faq.subcategory}` : ''}#${faq.id}`}
                    className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                  >
                    {isZh ? '查看答案' : 'View Answer'} →
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default function SearchPage({ params }: SearchPageProps) {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading search...</p>
      </div>
    </div>}>
      <SearchContent params={params} />
    </Suspense>
  )
}
