import { Metadata } from 'next'

interface HomePageProps {
  params: Promise<{ locale: string }>
}

export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'

  return {
    title: isZh ? 'G6PD缺乏症（蚕豆病）FAQ - 专业医疗指导和用药安全' : 'G6PD Deficiency (Favism) FAQ - Professional Medical Guidance and Medication Safety',
    description: isZh
      ? '专业的G6PD缺乏症（蚕豆病）医疗指导网站。提供用药禁忌、症状识别、饮食指导、治疗方案等全面信息。基于长尾关键词优化，帮助患者安全用药，避免溶血危机。'
      : 'Professional G6PD deficiency (favism) medical guidance website. Comprehensive information on medication contraindications, symptom recognition, dietary guidance, and treatment options. Optimized for long-tail keywords to help patients use medications safely and avoid hemolytic crises.',
    keywords: isZh
      ? ['G6PD缺乏症', '蚕豆病', '用药禁忌', '中药安全', '口服液', '症状识别', '饮食指导', '治疗方案', '溶血性贫血', '遗传病']
      : ['G6PD deficiency', 'favism', 'medication contraindications', 'Chinese medicine safety', 'oral solutions', 'symptom recognition', 'dietary guidance', 'treatment options', 'hemolytic anemia', 'genetic disorder'],
    openGraph: {
      title: isZh ? 'G6PD缺乏症（蚕豆病）FAQ - 专业医疗指导' : 'G6PD Deficiency FAQ - Professional Medical Guidance',
      description: isZh
        ? '专业的G6PD缺乏症医疗指导，包含用药安全、症状识别、饮食建议等全面信息'
        : 'Professional G6PD deficiency medical guidance with comprehensive medication safety, symptom recognition, and dietary advice',
      type: 'website',
      locale: locale,
      alternateLocale: locale === 'zh' ? 'en' : 'zh',
    },
    alternates: {
      canonical: `/${locale}`,
      languages: {
        'zh': '/zh',
        'en': '/en',
      },
    },
  }
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = await params
  const isZh = locale === 'zh'

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 text-gray-900 leading-tight">
              {isZh ? 'G6PD缺乏症（蚕豆病）' : 'G6PD Deficiency (Favism)'}
              <span className="block text-blue-600 mt-2">
                {isZh ? '专业指导中心' : 'Professional Guide Center'}
              </span>
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl mb-6 sm:mb-8 max-w-3xl mx-auto text-gray-600 leading-relaxed px-2">
              {isZh
                ? '为患者及家属提供权威、全面的医疗信息和生活指导，包括用药安全、饮食建议、症状识别等专业内容'
                : 'Providing authoritative and comprehensive medical information and lifestyle guidance for patients and families, including medication safety, dietary advice, symptom recognition and more'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
              <a
                href={`/${locale}/faq`}
                className="bg-blue-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center min-h-[48px] flex items-center justify-center"
              >
                {isZh ? '浏览常见问题' : 'Browse FAQ'}
              </a>
              <a
                href={`/${locale}/medications`}
                className="bg-white text-blue-600 border-2 border-blue-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center min-h-[48px] flex items-center justify-center"
              >
                {isZh ? '用药指导' : 'Medication Guide'}
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Access Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 hover:shadow-lg transition-shadow">
            <div className="text-red-600 mb-4">
              <svg className="w-10 h-10 sm:w-12 sm:h-12" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg sm:text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '中药禁忌' : 'Chinese Medicine Contraindications'}
            </h3>
            <p className="text-gray-600 mb-4 text-sm sm:text-base leading-relaxed">
              {isZh
                ? '了解G6PD缺乏症患者不能使用的中药成分和药物，避免溶血性贫血发作'
                : 'Learn about Chinese medicine ingredients and drugs that G6PD deficient patients cannot use to avoid hemolytic anemia attacks'
              }
            </p>
            <a
              href={`/${locale}/faq/medications/chinese-medicine`}
              className="text-blue-600 font-medium hover:text-blue-800 inline-flex items-center min-h-[44px] py-2"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>

          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 hover:shadow-lg transition-shadow">
            <div className="text-orange-600 mb-4">
              <svg className="w-10 h-10 sm:w-12 sm:h-12" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg sm:text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '口服液安全' : 'Oral Solution Safety'}
            </h3>
            <p className="text-gray-600 mb-4 text-sm sm:text-base leading-relaxed">
              {isZh
                ? '查询各种口服液药物的安全性，包括儿童常用的感冒药、退烧药等'
                : 'Check the safety of various oral solution medications, including common cold and fever medicines for children'
              }
            </p>
            <a
              href={`/${locale}/faq/medications/oral-solutions`}
              className="text-blue-600 font-medium hover:text-blue-800 inline-flex items-center min-h-[44px] py-2"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>

          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 hover:shadow-lg transition-shadow">
            <div className="text-green-600 mb-4">
              <svg className="w-10 h-10 sm:w-12 sm:h-12" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg sm:text-xl font-semibold mb-3 text-gray-900">
              {isZh ? '症状识别' : 'Symptom Recognition'}
            </h3>
            <p className="text-gray-600 mb-4 text-sm sm:text-base leading-relaxed">
              {isZh
                ? '学习识别G6PD缺乏症的症状表现，了解何时需要紧急就医'
                : 'Learn to recognize symptoms of G6PD deficiency and understand when emergency medical care is needed'
              }
            </p>
            <a
              href={`/${locale}/faq/symptoms`}
              className="text-blue-600 font-medium hover:text-blue-800 inline-flex items-center min-h-[44px] py-2"
            >
              {isZh ? '查看详情 →' : 'View Details →'}
            </a>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold mb-4">
              {isZh ? '关于G6PD缺乏症' : 'About G6PD Deficiency'}
            </h2>
            <p className="text-lg sm:text-xl text-blue-100 px-2">
              {isZh
                ? '了解这种常见的遗传性疾病的基本信息'
                : 'Learn basic information about this common genetic disorder'
              }
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 text-center">
            <div className="py-4">
              <div className="text-3xl sm:text-4xl font-bold mb-2">4-5%</div>
              <div className="text-blue-100 text-sm sm:text-base">
                {isZh ? '全球患病率' : 'Global Prevalence'}
              </div>
            </div>
            <div className="py-4">
              <div className="text-3xl sm:text-4xl font-bold mb-2">400M+</div>
              <div className="text-blue-100 text-sm sm:text-base">
                {isZh ? '全球患者数量' : 'Global Patient Count'}
              </div>
            </div>
            <div className="py-4">
              <div className="text-3xl sm:text-4xl font-bold mb-2">X-linked</div>
              <div className="text-blue-100 text-sm sm:text-base">
                {isZh ? '遗传方式' : 'Inheritance Pattern'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
