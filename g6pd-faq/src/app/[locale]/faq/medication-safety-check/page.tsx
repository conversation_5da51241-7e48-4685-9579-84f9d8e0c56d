import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '如何检查药物安全性？ - G6PD缺乏症FAQ' : 'How to Check Medication Safety? - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细指导G6PD缺乏症患者如何检查药物安全性，包括成分识别、资源查询和专业咨询方法。'
      : 'Detailed guidance on how G6PD deficient patients can check medication safety, including ingredient identification, resource consultation, and professional advice methods.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,药物安全检查,成分识别,用药安全,药物查询'
      : 'G6PD deficiency,medication safety check,ingredient identification,drug safety,medication lookup'
  }
}

export default async function MedicationSafetyCheckPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const checkSteps = [
    {
      step: 1,
      title: locale === 'zh' ? '阅读药品说明书' : 'Read Medication Labels',
      description: locale === 'zh' ? '仔细查看药品包装和说明书上的成分表' : 'Carefully check ingredient lists on medication packaging and instructions',
      details: [
        locale === 'zh' ? '查看"成分"或"组成"部分' : 'Check "Ingredients" or "Composition" section',
        locale === 'zh' ? '注意通用名和商品名' : 'Note generic and brand names',
        locale === 'zh' ? '留意辅料成分' : 'Pay attention to excipient ingredients',
        locale === 'zh' ? '查看禁忌症说明' : 'Review contraindication information'
      ]
    },
    {
      step: 2,
      title: locale === 'zh' ? '识别危险成分' : 'Identify Dangerous Ingredients',
      description: locale === 'zh' ? '对照已知的G6PD缺乏症禁用药物清单' : 'Compare with known G6PD deficiency prohibited drug lists',
      details: [
        locale === 'zh' ? '检查是否含有磺胺类药物' : 'Check for sulfonamide drugs',
        locale === 'zh' ? '查找薄荷脑、樟脑等成分' : 'Look for menthol, camphor, and similar ingredients',
        locale === 'zh' ? '注意抗疟药成分' : 'Watch for antimalarial components',
        locale === 'zh' ? '识别某些抗生素' : 'Identify certain antibiotics'
      ]
    },
    {
      step: 3,
      title: locale === 'zh' ? '查询专业资源' : 'Consult Professional Resources',
      description: locale === 'zh' ? '使用可靠的医学资源进行验证' : 'Use reliable medical resources for verification',
      details: [
        locale === 'zh' ? '咨询医生或药师' : 'Consult doctors or pharmacists',
        locale === 'zh' ? '查阅医学数据库' : 'Check medical databases',
        locale === 'zh' ? '使用官方药物查询工具' : 'Use official drug lookup tools',
        locale === 'zh' ? '参考权威医学指南' : 'Reference authoritative medical guidelines'
      ]
    },
    {
      step: 4,
      title: locale === 'zh' ? '做出安全决定' : 'Make Safe Decisions',
      description: locale === 'zh' ? '基于收集的信息做出用药决定' : 'Make medication decisions based on collected information',
      details: [
        locale === 'zh' ? '有疑虑时选择不使用' : 'Choose not to use when in doubt',
        locale === 'zh' ? '寻找安全的替代药物' : 'Look for safe alternative medications',
        locale === 'zh' ? '记录安全和不安全的药物' : 'Record safe and unsafe medications',
        locale === 'zh' ? '与医疗团队保持沟通' : 'Maintain communication with medical team'
      ]
    }
  ]

  const redFlags = [
    {
      category: locale === 'zh' ? '成分名称' : 'Ingredient Names',
      warnings: [
        locale === 'zh' ? '含有"磺胺"字样' : 'Contains "sulfa" or "sulfon"',
        locale === 'zh' ? '含有"薄荷"、"樟脑"' : 'Contains "menthol" or "camphor"',
        locale === 'zh' ? '含有"牛黄"、"雄黄"' : 'Contains "Calculus Bovis" or "Realgar"',
        locale === 'zh' ? '含有"奎宁"相关成分' : 'Contains "quinine" related components'
      ]
    },
    {
      category: locale === 'zh' ? '药物类型' : 'Medication Types',
      warnings: [
        locale === 'zh' ? '抗疟药物' : 'Antimalarial drugs',
        locale === 'zh' ? '某些抗生素' : 'Certain antibiotics',
        locale === 'zh' ? '含薄荷的止咳药' : 'Menthol-containing cough medicines',
        locale === 'zh' ? '某些解热镇痛药' : 'Certain analgesics'
      ]
    },
    {
      category: locale === 'zh' ? '包装标识' : 'Package Labels',
      warnings: [
        locale === 'zh' ? '缺少完整成分表' : 'Missing complete ingredient list',
        locale === 'zh' ? '标注"G6PD患者禁用"' : 'Labeled "Contraindicated for G6PD patients"',
        locale === 'zh' ? '复方制剂成分复杂' : 'Complex compound formulations',
        locale === 'zh' ? '进口药品标识不清' : 'Unclear labeling on imported drugs'
      ]
    }
  ]

  const resources = [
    {
      type: locale === 'zh' ? '医疗专业人员' : 'Medical Professionals',
      options: [
        {
          name: locale === 'zh' ? '血液科医生' : 'Hematologist',
          description: locale === 'zh' ? 'G6PD缺乏症专科医生' : 'G6PD deficiency specialist',
          when: locale === 'zh' ? '诊断确认和治疗方案' : 'Diagnosis confirmation and treatment plans'
        },
        {
          name: locale === 'zh' ? '临床药师' : 'Clinical Pharmacist',
          description: locale === 'zh' ? '药物相互作用和安全性专家' : 'Drug interaction and safety expert',
          when: locale === 'zh' ? '药物选择和剂量调整' : 'Medication selection and dosage adjustment'
        },
        {
          name: locale === 'zh' ? '儿科医生' : 'Pediatrician',
          description: locale === 'zh' ? '儿童用药专家' : 'Pediatric medication expert',
          when: locale === 'zh' ? '儿童患者用药指导' : 'Medication guidance for pediatric patients'
        }
      ]
    },
    {
      type: locale === 'zh' ? '在线资源' : 'Online Resources',
      options: [
        {
          name: locale === 'zh' ? '国家药监局数据库' : 'National Drug Administration Database',
          description: locale === 'zh' ? '官方药品信息查询' : 'Official drug information lookup',
          when: locale === 'zh' ? '验证药品合法性和成分' : 'Verify drug legitimacy and ingredients'
        },
        {
          name: locale === 'zh' ? '医学文献数据库' : 'Medical Literature Database',
          description: locale === 'zh' ? '最新研究和指南' : 'Latest research and guidelines',
          when: locale === 'zh' ? '了解最新安全信息' : 'Learn latest safety information'
        },
        {
          name: locale === 'zh' ? '患者组织网站' : 'Patient Organization Websites',
          description: locale === 'zh' ? '患者经验分享' : 'Patient experience sharing',
          when: locale === 'zh' ? '获取实用建议' : 'Get practical advice'
        }
      ]
    }
  ]

  const practicalTips = [
    {
      situation: locale === 'zh' ? '急诊情况' : 'Emergency Situations',
      tips: [
        locale === 'zh' ? '随身携带G6PD缺乏症诊断证明' : 'Carry G6PD deficiency diagnosis certificate',
        locale === 'zh' ? '告知医护人员您的病情' : 'Inform medical staff about your condition',
        locale === 'zh' ? '提供禁用药物清单' : 'Provide list of prohibited medications',
        locale === 'zh' ? '要求使用安全替代药物' : 'Request safe alternative medications'
      ]
    },
    {
      situation: locale === 'zh' ? '日常购药' : 'Routine Medication Purchase',
      tips: [
        locale === 'zh' ? '选择熟悉的药店' : 'Choose familiar pharmacies',
        locale === 'zh' ? '与药师建立良好关系' : 'Build good relationships with pharmacists',
        locale === 'zh' ? '保存安全药物清单' : 'Keep a list of safe medications',
        locale === 'zh' ? '避免网购不明药品' : 'Avoid buying unknown drugs online'
      ]
    },
    {
      situation: locale === 'zh' ? '出国旅行' : 'International Travel',
      tips: [
        locale === 'zh' ? '准备英文诊断证明' : 'Prepare English diagnosis certificate',
        locale === 'zh' ? '了解目的地医疗资源' : 'Learn about destination medical resources',
        locale === 'zh' ? '携带足够的安全药物' : 'Carry sufficient safe medications',
        locale === 'zh' ? '购买适当的旅行保险' : 'Purchase appropriate travel insurance'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <MagnifyingGlassIcon className="h-16 w-16 mx-auto mb-6 opacity-90" />
            <h1 className="text-3xl md:text-4xl font-bold mb-6">
              {locale === 'zh' ? '如何检查药物安全性？' : 'How to Check Medication Safety?'}
            </h1>
            <p className="text-xl opacity-90">
              {locale === 'zh' 
                ? '学习系统性的药物安全检查方法，保护您的健康'
                : 'Learn systematic medication safety checking methods to protect your health'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Quick Guide */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-green-50 border-l-4 border-green-400 p-6 rounded-lg">
            <div className="flex items-start">
              <CheckCircleIcon className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
              <div>
                <h2 className="text-xl font-semibold text-green-800 mb-3">
                  {locale === 'zh' ? '快速检查要点' : 'Quick Check Points'}
                </h2>
                <p className="text-green-700 leading-relaxed">
                  {locale === 'zh' 
                    ? '使用任何药物前：1) 仔细阅读成分表；2) 对照禁用药物清单；3) 有疑问时咨询医生；4) 记录安全和不安全的药物。记住：有疑虑时宁可不用。'
                    : 'Before using any medication: 1) Carefully read ingredient lists; 2) Compare with prohibited drug lists; 3) Consult doctors when in doubt; 4) Record safe and unsafe medications. Remember: when in doubt, don\'t use.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Check Steps */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '药物安全检查步骤' : 'Medication Safety Check Steps'}
          </h2>

          <div className="space-y-8">
            {checkSteps.map((step, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <div className="flex items-start mb-4">
                  <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0">
                    {step.step}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-blue-800 mb-2">
                      {step.title}
                    </h3>
                    <p className="text-blue-700 mb-4">
                      {step.description}
                    </p>
                  </div>
                </div>
                <div className="ml-12">
                  <ul className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="text-blue-700 flex items-start">
                        <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-1 mr-2 flex-shrink-0" />
                        <span className="text-sm">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Red Flags */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '危险信号识别' : 'Red Flag Identification'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {redFlags.map((category, index) => (
              <div key={index} className="bg-red-50 rounded-lg p-6 border-l-4 border-red-500">
                <h3 className="text-lg font-semibold text-red-800 mb-4 flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
                  {category.category}
                </h3>
                <ul className="space-y-2">
                  {category.warnings.map((warning, warningIndex) => (
                    <li key={warningIndex} className="text-red-700 flex items-start">
                      <XCircleIcon className="h-4 w-4 text-red-600 mt-1 mr-2 flex-shrink-0" />
                      <span className="text-sm">{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Professional Resources */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '专业咨询资源' : 'Professional Consultation Resources'}
          </h2>

          <div className="space-y-8">
            {resources.map((resourceType, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">
                  {resourceType.type}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {resourceType.options.map((option, optionIndex) => (
                    <div key={optionIndex} className="bg-white p-4 rounded-lg border">
                      <h4 className="font-semibold text-gray-900 mb-2">
                        {option.name}
                      </h4>
                      <p className="text-gray-600 text-sm mb-3">
                        {option.description}
                      </p>
                      <p className="text-gray-700 text-sm">
                        <strong>{locale === 'zh' ? '适用于：' : 'Best for: '}</strong>
                        {option.when}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Practical Tips */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '实用建议' : 'Practical Tips'}
          </h2>

          <div className="space-y-6">
            {practicalTips.map((tipSection, index) => (
              <div key={index} className="bg-yellow-50 rounded-lg p-6 border-l-4 border-yellow-500">
                <h3 className="text-lg font-semibold text-yellow-800 mb-4">
                  {tipSection.situation}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {tipSection.tips.map((tip, tipIndex) => (
                    <div key={tipIndex} className="flex items-start">
                      <InformationCircleIcon className="h-4 w-4 text-yellow-600 mt-1 mr-2 flex-shrink-0" />
                      <span className="text-yellow-700 text-sm">{tip}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Checklist */}
      <section className="py-12 bg-red-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-red-900 mb-4">
              {locale === 'zh' ? '紧急情况检查清单' : 'Emergency Checklist'}
            </h2>
            <p className="text-red-700">
              {locale === 'zh'
                ? '在紧急情况下，请确保医护人员了解以下信息：'
                : 'In emergency situations, ensure medical staff are aware of the following:'
              }
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 border-l-4 border-red-500">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-red-800 mb-3">
                  {locale === 'zh' ? '必须告知的信息' : 'Essential Information to Share'}
                </h3>
                <ul className="space-y-2">
                  {[
                    locale === 'zh' ? '患有G6PD缺乏症' : 'Has G6PD deficiency',
                    locale === 'zh' ? '禁用磺胺类药物' : 'Contraindicated for sulfonamides',
                    locale === 'zh' ? '禁用某些抗疟药' : 'Contraindicated for certain antimalarials',
                    locale === 'zh' ? '禁用薄荷脑类药物' : 'Contraindicated for menthol-based drugs'
                  ].map((item, index) => (
                    <li key={index} className="text-red-700 flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-red-600 mr-2" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-red-800 mb-3">
                  {locale === 'zh' ? '随身携带物品' : 'Items to Carry'}
                </h3>
                <ul className="space-y-2">
                  {[
                    locale === 'zh' ? '诊断证明书' : 'Diagnosis certificate',
                    locale === 'zh' ? '禁用药物清单' : 'Prohibited drug list',
                    locale === 'zh' ? '安全药物清单' : 'Safe medication list',
                    locale === 'zh' ? '紧急联系人信息' : 'Emergency contact information'
                  ].map((item, index) => (
                    <li key={index} className="text-red-700 flex items-center">
                      <DocumentTextIcon className="h-4 w-4 text-red-600 mr-2" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '相关信息' : 'Related Information'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药用药指导' : 'Western Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解西药的安全使用信息' : 'Learn about safe western medicine usage'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/chinese-medicine`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '中药用药指导' : 'Chinese Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解中药的安全使用信息' : 'Learn about safe Chinese medicine usage'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/aspirin-safety`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '阿司匹林安全性' : 'Aspirin Safety'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解阿司匹林的使用注意事项' : 'Learn about aspirin usage precautions'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '识别药物不良反应症状' : 'Recognize adverse drug reaction symptoms'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh'
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何用药决定都应咨询合格的医疗专业人员。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any medication decisions should consult qualified medical professionals.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
