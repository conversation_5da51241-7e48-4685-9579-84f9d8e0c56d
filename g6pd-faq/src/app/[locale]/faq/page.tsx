import { Metadata } from 'next'
import Link from 'next/link'
import { faqCategories, allFaqData } from '@/lib/faq-data'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'

  const title = isZh ? '常见问题解答 - G6PD缺乏症（蚕豆病）' : 'FAQ - G6PD Deficiency (Favism)'
  const description = isZh
    ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，包括用药安全、症状识别、饮食指导等'
    : 'Professional answers about G6PD deficiency (favism), including medication safety, symptom recognition, dietary guidance and more'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function FAQPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const isZh = locale === 'zh'

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 leading-tight">
              {isZh ? '常见问题解答' : 'Frequently Asked Questions'}
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed px-2">
              {isZh
                ? '关于G6PD缺乏症（蚕豆病）的专业问题解答，帮助您更好地了解和管理这种疾病'
                : 'Professional answers about G6PD deficiency (favism) to help you better understand and manage this condition'
              }
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Categories */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12">
          {faqCategories[locale as 'zh' | 'en'].map((category) => (
            <Link
              key={category.id}
              href={`/${locale}/faq/${category.id}`}
              className="bg-white rounded-lg shadow-md p-4 sm:p-6 hover:shadow-lg transition-shadow group min-h-[120px] flex flex-col"
            >
              <div className="text-3xl sm:text-4xl mb-3 sm:mb-4">{category.icon}</div>
              <h3 className="text-lg sm:text-xl font-semibold mb-2 text-gray-900 group-hover:text-blue-600 flex-grow">
                {category.name}
              </h3>
              <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                {category.description}
              </p>
            </Link>
          ))}
        </div>

        {/* Popular Questions */}
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 lg:p-8">
          <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900">
            {isZh ? '热门问题' : 'Popular Questions'}
          </h2>
          <div className="space-y-4 sm:space-y-6">
            {allFaqData
              .filter(faq => faq.locale === locale)
              .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))
              .slice(0, 6)
              .map((faq) => (
                <div key={faq.id} className="border-b border-gray-200 pb-4 sm:pb-6 last:border-b-0">
                  <h3 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3 text-gray-900 leading-tight">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-sm sm:text-base">
                    {faq.answer}
                  </p>
                  <div className="mt-2 sm:mt-3 flex flex-wrap gap-1 sm:gap-2">
                    {faq.keywords.map((keyword) => (
                      <span
                        key={keyword}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Search Section */}
        <div className="mt-8 sm:mt-12 bg-blue-50 rounded-lg p-4 sm:p-6 lg:p-8 text-center">
          <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-gray-900">
            {isZh ? '找不到您要的答案？' : "Can't find what you're looking for?"}
          </h2>
          <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base px-2">
            {isZh
              ? '使用我们的搜索功能查找更多相关信息'
              : 'Use our search function to find more relevant information'
            }
          </p>
          <Link
            href={`/${locale}/search`}
            className="bg-blue-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block min-h-[48px] flex items-center justify-center"
          >
            {isZh ? '搜索FAQ' : 'Search FAQ'}
          </Link>
        </div>
      </div>
    </div>
  )
}
