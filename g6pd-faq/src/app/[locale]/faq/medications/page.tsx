import { Metadata } from 'next'
import Link from 'next/link'
import { faqCategories, getFaqsByCategory } from '@/lib/faq-data'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const title = isZh ? '用药安全 - G6PD缺乏症（蚕豆病）' : 'Medication Safety - G6PD Deficiency'
  const description = isZh 
    ? 'G6PD缺乏症患者的用药安全指导，包括中药禁忌、口服液安全性、西药使用注意事项等'
    : 'Medication safety guidance for G6PD deficiency patients, including Chinese medicine contraindications, oral solution safety, western medicine precautions'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function MedicationsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const category = faqCategories[locale as 'zh' | 'en'].find(cat => cat.id === 'medications')
  const faqs = getFaqsByCategory('medications', locale as 'zh' | 'en')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href={`/${locale}`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '首页' : 'Home'}
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <Link href={`/${locale}/faq`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '常见问题' : 'FAQ'}
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">
                  {category?.name}
                </span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-red-500 to-pink-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">{category?.icon}</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {category?.name}
            </h1>
            <p className="text-xl text-red-100 max-w-3xl mx-auto">
              {category?.description}
            </p>
          </div>
        </div>
      </div>

      {/* Subcategories */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {category?.subcategories && (
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {category.subcategories.map((subcategory) => (
              <Link
                key={subcategory.id}
                href={`/${locale}/faq/medications/${subcategory.id}`}
                className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group"
              >
                <h3 className="text-xl font-semibold mb-2 text-gray-900 group-hover:text-red-600">
                  {subcategory.name}
                </h3>
                <p className="text-gray-600 text-sm">
                  {subcategory.description}
                </p>
                <div className="mt-4 text-red-600 font-medium">
                  {isZh ? '查看详情 →' : 'View Details →'}
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* FAQ List */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {isZh ? '用药安全问题' : 'Medication Safety Questions'}
          </h2>
          <div className="space-y-6">
            {faqs.map((faq) => (
              <div key={faq.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-3">
                  {faq.answer}
                </p>
                <div className="flex flex-wrap gap-2">
                  {faq.keywords.map((keyword) => (
                    <span
                      key={keyword}
                      className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full"
                    >
                      {keyword}
                    </span>
                  ))}
                  {faq.searchVolume && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {isZh ? '搜索量' : 'Search Volume'}: {faq.searchVolume.toLocaleString()}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Warning Section */}
        <div className="mt-12 bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                {isZh ? '重要提醒' : 'Important Notice'}
              </h3>
              <p className="text-red-700">
                {isZh 
                  ? '本页面提供的信息仅供参考，不能替代专业医疗建议。G6PD缺乏症患者在使用任何药物前，请务必咨询医生或药师，并告知您的G6PD缺乏症状况。如出现溶血症状（如面色苍白、乏力、尿色加深等），请立即就医。'
                  : 'The information provided on this page is for reference only and cannot replace professional medical advice. G6PD deficiency patients must consult a doctor or pharmacist before using any medication and inform them of your G6PD deficiency status. If hemolytic symptoms occur (such as pallor, fatigue, dark urine), seek immediate medical attention.'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
