import { Metadata } from 'next'
import Link from 'next/link'
import { getFaqsBySubcategory } from '@/lib/faq-data'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const title = isZh ? '中药禁忌 - G6PD缺乏症（蚕豆病）' : 'Chinese Medicine Contraindications - G6PD Deficiency'
  const description = isZh 
    ? 'G6PD缺乏症患者不能使用的中药成分和药物详细列表，避免溶血性贫血发作'
    : 'Detailed list of Chinese medicine ingredients and drugs that G6PD deficiency patients cannot use to avoid hemolytic anemia attacks'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function ChineseMedicinePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const faqs = getFaqsBySubcategory('medications', 'chinese-medicine', locale as 'zh' | 'en')

  // Prohibited Chinese medicines data
  const prohibitedMedicines = {
    zh: [
      {
        category: '清热解毒类',
        medicines: ['薄荷', '金银花', '黄连', '黄芩', '板蓝根', '大青叶', '蒲公英'],
        risk: '高风险'
      },
      {
        category: '泻下类',
        medicines: ['大黄', '芒硝', '番泻叶'],
        risk: '高风险'
      },
      {
        category: '发散风寒类',
        medicines: ['麻黄', '桂枝', '紫苏叶'],
        risk: '中等风险'
      },
      {
        category: '利水渗湿类',
        medicines: ['茵陈', '车前子', '泽泻'],
        risk: '中等风险'
      }
    ],
    en: [
      {
        category: 'Heat-clearing and Detoxifying',
        medicines: ['Mint', 'Honeysuckle', 'Coptis', 'Scutellaria', 'Isatis Root', 'Isatis Leaf', 'Dandelion'],
        risk: 'High Risk'
      },
      {
        category: 'Purgative',
        medicines: ['Rhubarb', 'Mirabilite', 'Senna Leaf'],
        risk: 'High Risk'
      },
      {
        category: 'Wind-Cold Dispersing',
        medicines: ['Ephedra', 'Cinnamon Twig', 'Perilla Leaf'],
        risk: 'Moderate Risk'
      },
      {
        category: 'Water-promoting and Dampness-permeating',
        medicines: ['Artemisia', 'Plantain Seed', 'Alisma'],
        risk: 'Moderate Risk'
      }
    ]
  }

  const safeMedicines = {
    zh: [
      {
        category: '补益类',
        medicines: ['人参', '党参', '黄芪', '当归', '熟地黄', '白芍', '阿胶']
      },
      {
        category: '健脾类',
        medicines: ['白术', '茯苓', '山药', '陈皮', '半夏']
      },
      {
        category: '安神类',
        medicines: ['酸枣仁', '龙骨', '牡蛎', '远志']
      }
    ],
    en: [
      {
        category: 'Tonifying',
        medicines: ['Ginseng', 'Codonopsis', 'Astragalus', 'Angelica', 'Prepared Rehmannia', 'White Peony', 'Donkey-hide Gelatin']
      },
      {
        category: 'Spleen-strengthening',
        medicines: ['Atractylodes', 'Poria', 'Chinese Yam', 'Tangerine Peel', 'Pinellia']
      },
      {
        category: 'Spirit-calming',
        medicines: ['Jujube Seed', 'Dragon Bone', 'Oyster Shell', 'Polygala']
      }
    ]
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href={`/${locale}`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '首页' : 'Home'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <Link href={`/${locale}/faq`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '常见问题' : 'FAQ'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <Link href={`/${locale}/faq/medications`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '用药安全' : 'Medication Safety'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <span className="text-gray-900 font-medium">
                  {isZh ? '中药禁忌' : 'Chinese Medicine'}
                </span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-red-600 to-red-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">🚫</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {isZh ? '中药禁忌指南' : 'Chinese Medicine Contraindications Guide'}
            </h1>
            <p className="text-xl text-red-100 max-w-3xl mx-auto">
              {isZh 
                ? 'G6PD缺乏症患者必须了解的中药使用禁忌，避免溶血性贫血发作'
                : 'Essential Chinese medicine contraindications for G6PD deficiency patients to prevent hemolytic anemia attacks'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Prohibited Medicines */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6 text-red-800 flex items-center">
            <svg className="w-8 h-8 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
            </svg>
            {isZh ? '禁用中药列表' : 'Prohibited Chinese Medicines'}
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            {prohibitedMedicines[locale as 'zh' | 'en'].map((category, index) => (
              <div key={index} className="bg-white rounded-lg p-6 border border-red-200">
                <h3 className="text-lg font-semibold mb-3 text-red-800">
                  {category.category}
                </h3>
                <div className="mb-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    category.risk === '高风险' || category.risk === 'High Risk' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {category.risk}
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {category.medicines.map((medicine) => (
                    <span
                      key={medicine}
                      className="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full"
                    >
                      {medicine}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Safe Medicines */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6 text-green-800 flex items-center">
            <svg className="w-8 h-8 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {isZh ? '相对安全的中药' : 'Relatively Safe Chinese Medicines'}
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            {safeMedicines[locale as 'zh' | 'en'].map((category, index) => (
              <div key={index} className="bg-white rounded-lg p-6 border border-green-200">
                <h3 className="text-lg font-semibold mb-3 text-green-800">
                  {category.category}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {category.medicines.map((medicine) => (
                    <span
                      key={medicine}
                      className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full"
                    >
                      {medicine}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {isZh ? '中药使用常见问题' : 'Common Questions About Chinese Medicine Use'}
          </h2>
          <div className="space-y-6">
            {faqs.map((faq) => (
              <div key={faq.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-3">
                  {faq.answer}
                </p>
                <div className="flex flex-wrap gap-2">
                  {faq.keywords.map((keyword) => (
                    <span
                      key={keyword}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Emergency Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                {isZh ? '紧急情况处理' : 'Emergency Situation Management'}
              </h3>
              <p className="text-yellow-700">
                {isZh 
                  ? '如果误用禁忌中药后出现面色苍白、乏力、尿色加深、黄疸等症状，请立即停用药物并紧急就医。告知医生您的G6PD缺乏症状况和所使用的中药。'
                  : 'If you experience pallor, fatigue, dark urine, jaundice or other symptoms after mistakenly using contraindicated Chinese medicine, immediately stop the medication and seek emergency medical care. Inform the doctor of your G6PD deficiency status and the Chinese medicine used.'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
