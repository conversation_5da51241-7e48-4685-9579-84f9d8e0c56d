import { Metadata } from 'next'
import Link from 'next/link'
import { getFaqsBySubcategory } from '@/lib/faq-data'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const title = isZh ? '口服液安全 - G6PD缺乏症（蚕豆病）' : 'Oral Solution Safety - G6PD Deficiency'
  const description = isZh 
    ? 'G6PD缺乏症患者各种口服液药物的安全性评估，包括儿童常用感冒药、退烧药等'
    : 'Safety assessment of various oral solution medications for G6PD deficiency patients, including common cold and fever medicines for children'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function OralSolutionsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  const faqs = getFaqsBySubcategory('medications', 'oral-solutions', locale as 'zh' | 'en')

  // Oral solutions safety data
  const oralSolutions = {
    zh: [
      {
        category: '感冒类口服液',
        medicines: [
          { name: '双黄连口服液', safety: '不安全', reason: '含金银花、黄芩等禁忌成分' },
          { name: '抗病毒口服液', safety: '需谨慎', reason: '部分含板蓝根等成分' },
          { name: '小儿感冒颗粒', safety: '需谨慎', reason: '查看具体成分' },
          { name: '999感冒灵', safety: '相对安全', reason: '主要成分相对安全' }
        ]
      },
      {
        category: '止咳类口服液',
        medicines: [
          { name: '川贝枇杷膏', safety: '相对安全', reason: '主要成分川贝、枇杷叶安全' },
          { name: '急支糖浆', safety: '需谨慎', reason: '含鱼腥草等成分' },
          { name: '蛇胆川贝液', safety: '相对安全', reason: '主要成分安全' },
          { name: '复方甘草口服液', safety: '相对安全', reason: '甘草成分相对安全' }
        ]
      },
      {
        category: '消化类口服液',
        medicines: [
          { name: '保济口服液', safety: '需谨慎', reason: '含薄荷等成分' },
          { name: '藿香正气液', safety: '需谨慎', reason: '含藿香、紫苏等成分' },
          { name: '健胃消食口服液', safety: '相对安全', reason: '主要成分相对安全' },
          { name: '妈咪爱', safety: '安全', reason: '益生菌制剂，安全' }
        ]
      }
    ],
    en: [
      {
        category: 'Cold Medicine Oral Solutions',
        medicines: [
          { name: 'Shuanghuanglian Oral Solution', safety: 'Unsafe', reason: 'Contains honeysuckle, scutellaria and other contraindicated ingredients' },
          { name: 'Antiviral Oral Solution', safety: 'Use with Caution', reason: 'Some contain isatis root and other ingredients' },
          { name: 'Pediatric Cold Granules', safety: 'Use with Caution', reason: 'Check specific ingredients' },
          { name: '999 Cold Medicine', safety: 'Relatively Safe', reason: 'Main ingredients are relatively safe' }
        ]
      },
      {
        category: 'Cough Medicine Oral Solutions',
        medicines: [
          { name: 'Fritillaria Loquat Syrup', safety: 'Relatively Safe', reason: 'Main ingredients fritillaria and loquat leaf are safe' },
          { name: 'Jizhitang Syrup', safety: 'Use with Caution', reason: 'Contains houttuynia and other ingredients' },
          { name: 'Snake Gallbladder Fritillaria Solution', safety: 'Relatively Safe', reason: 'Main ingredients are safe' },
          { name: 'Compound Licorice Oral Solution', safety: 'Relatively Safe', reason: 'Licorice ingredients are relatively safe' }
        ]
      },
      {
        category: 'Digestive Oral Solutions',
        medicines: [
          { name: 'Baoji Oral Solution', safety: 'Use with Caution', reason: 'Contains mint and other ingredients' },
          { name: 'Huoxiang Zhengqi Solution', safety: 'Use with Caution', reason: 'Contains agastache, perilla and other ingredients' },
          { name: 'Stomach-strengthening Digestive Oral Solution', safety: 'Relatively Safe', reason: 'Main ingredients are relatively safe' },
          { name: 'Mamiai', safety: 'Safe', reason: 'Probiotic preparation, safe' }
        ]
      }
    ]
  }

  const getSafetyColor = (safety: string) => {
    if (safety === '安全' || safety === 'Safe') return 'bg-green-100 text-green-800'
    if (safety === '相对安全' || safety === 'Relatively Safe') return 'bg-blue-100 text-blue-800'
    if (safety === '需谨慎' || safety === 'Use with Caution') return 'bg-yellow-100 text-yellow-800'
    if (safety === '不安全' || safety === 'Unsafe') return 'bg-red-100 text-red-800'
    return 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href={`/${locale}`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '首页' : 'Home'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <Link href={`/${locale}/faq`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '常见问题' : 'FAQ'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <Link href={`/${locale}/faq/medications`} className="text-gray-500 hover:text-gray-700">
                  {isZh ? '用药安全' : 'Medication Safety'}
                </Link>
              </li>
              <li><span className="text-gray-400">/</span></li>
              <li>
                <span className="text-gray-900 font-medium">
                  {isZh ? '口服液安全' : 'Oral Solutions'}
                </span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-red-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">🧪</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {isZh ? '口服液安全指南' : 'Oral Solution Safety Guide'}
            </h1>
            <p className="text-xl text-orange-100 max-w-3xl mx-auto">
              {isZh 
                ? 'G6PD缺乏症患者各种口服液药物的安全性评估和使用指导'
                : 'Safety assessment and usage guidance for various oral solution medications for G6PD deficiency patients'
              }
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Safety Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {isZh ? '口服液安全性分类' : 'Oral Solution Safety Categories'}
          </h2>
          <div className="grid md:grid-cols-4 gap-4 mb-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <div className="text-green-600 font-semibold mb-2">{isZh ? '安全' : 'Safe'}</div>
              <div className="text-sm text-green-700">{isZh ? '可以正常使用' : 'Can be used normally'}</div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
              <div className="text-blue-600 font-semibold mb-2">{isZh ? '相对安全' : 'Relatively Safe'}</div>
              <div className="text-sm text-blue-700">{isZh ? '建议医生指导下使用' : 'Recommended under doctor guidance'}</div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
              <div className="text-yellow-600 font-semibold mb-2">{isZh ? '需谨慎' : 'Use with Caution'}</div>
              <div className="text-sm text-yellow-700">{isZh ? '仔细查看成分，小心使用' : 'Check ingredients carefully, use with care'}</div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
              <div className="text-red-600 font-semibold mb-2">{isZh ? '不安全' : 'Unsafe'}</div>
              <div className="text-sm text-red-700">{isZh ? '避免使用' : 'Avoid use'}</div>
            </div>
          </div>
        </div>

        {/* Oral Solutions List */}
        <div className="space-y-8 mb-12">
          {oralSolutions[locale as 'zh' | 'en'].map((category, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-8">
              <h3 className="text-xl font-bold mb-6 text-gray-900">
                {category.category}
              </h3>
              <div className="grid md:grid-cols-2 gap-4">
                {category.medicines.map((medicine, medIndex) => (
                  <div key={medIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-900">{medicine.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSafetyColor(medicine.safety)}`}>
                        {medicine.safety}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{medicine.reason}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {isZh ? '口服液使用常见问题' : 'Common Questions About Oral Solution Use'}
          </h2>
          <div className="space-y-6">
            {faqs.map((faq) => (
              <div key={faq.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-3">
                  {faq.answer}
                </p>
                <div className="flex flex-wrap gap-2">
                  {faq.keywords.map((keyword) => (
                    <span
                      key={keyword}
                      className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Usage Guidelines */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-4">
            {isZh ? '使用指导原则' : 'Usage Guidelines'}
          </h3>
          <ul className="space-y-2 text-blue-700">
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              {isZh 
                ? '使用任何口服液前，仔细阅读成分表，避免含有禁忌成分的产品'
                : 'Before using any oral solution, carefully read the ingredient list and avoid products containing contraindicated ingredients'
              }
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              {isZh 
                ? '首次使用新的口服液时，建议小剂量试用，观察身体反应'
                : 'When using a new oral solution for the first time, it is recommended to try a small dose and observe body reactions'
              }
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              {isZh 
                ? '如出现面色苍白、乏力、尿色加深等症状，立即停用并就医'
                : 'If symptoms such as pallor, fatigue, or dark urine occur, stop use immediately and seek medical attention'
              }
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              {isZh 
                ? '购买药物时告知药师您的G6PD缺乏症状况，寻求专业建议'
                : 'When purchasing medications, inform the pharmacist of your G6PD deficiency status and seek professional advice'
              }
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
