import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '儿童口服液安全指导 - G6PD缺乏症FAQ' : 'Children\'s Oral Solution Safety Guide - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细指导G6PD缺乏症儿童如何安全使用口服液药物，包括安全产品推荐、禁用成分和用药注意事项。'
      : 'Detailed guidance on safe oral solution usage for children with G6PD deficiency, including safe product recommendations, prohibited ingredients, and precautions.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,儿童口服液,儿童用药,口服液安全,小儿用药'
      : 'G6PD deficiency,children oral solutions,pediatric medication,oral solution safety,child medication'
  }
}

export default async function ChildrenOralSolutionsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const safeOralSolutions = [
    {
      category: locale === 'zh' ? '退热止痛类' : 'Fever and Pain Relief',
      products: [
        {
          name: locale === 'zh' ? '对乙酰氨基酚口服液' : 'Acetaminophen Oral Solution',
          brand: locale === 'zh' ? '泰诺林、百服宁等' : 'Tylenol, Panadol, etc.',
          safety: 'safe',
          dosage: locale === 'zh' ? '10-15mg/kg，每4-6小时一次' : '10-15mg/kg every 4-6 hours',
          notes: locale === 'zh' ? '首选退热药物，相对最安全' : 'First choice for fever, relatively safest'
        },
        {
          name: locale === 'zh' ? '布洛芬口服液' : 'Ibuprofen Oral Solution',
          brand: locale === 'zh' ? '美林、芬必得等' : 'Advil, Nurofen, etc.',
          safety: 'caution',
          dosage: locale === 'zh' ? '5-10mg/kg，每6-8小时一次' : '5-10mg/kg every 6-8 hours',
          notes: locale === 'zh' ? '6个月以上儿童可用，需医生指导' : 'For children over 6 months, requires medical guidance'
        }
      ]
    },
    {
      category: locale === 'zh' ? '止咳化痰类' : 'Cough and Expectorant',
      products: [
        {
          name: locale === 'zh' ? '氨溴索口服液' : 'Ambroxol Oral Solution',
          brand: locale === 'zh' ? '沐舒坦、易坦静等' : 'Mucosolvan, Ambrobene, etc.',
          safety: 'safe',
          dosage: locale === 'zh' ? '按年龄和体重调整' : 'Adjust according to age and weight',
          notes: locale === 'zh' ? '化痰效果好，安全性高' : 'Good expectorant effect, high safety'
        },
        {
          name: locale === 'zh' ? '右美沙芬口服液' : 'Dextromethorphan Oral Solution',
          brand: locale === 'zh' ? '惠菲宁等' : 'Robitussin DM, etc.',
          safety: 'safe',
          dosage: locale === 'zh' ? '2岁以上儿童使用' : 'For children over 2 years',
          notes: locale === 'zh' ? '干咳首选，无成瘾性' : 'First choice for dry cough, non-addictive'
        }
      ]
    },
    {
      category: locale === 'zh' ? '胃肠道类' : 'Gastrointestinal',
      products: [
        {
          name: locale === 'zh' ? '蒙脱石散口服液' : 'Montmorillonite Oral Suspension',
          brand: locale === 'zh' ? '思密达等' : 'Smecta, etc.',
          safety: 'safe',
          dosage: locale === 'zh' ? '按说明书或医嘱使用' : 'Use according to instructions or prescription',
          notes: locale === 'zh' ? '治疗腹泻，安全有效' : 'For diarrhea treatment, safe and effective'
        },
        {
          name: locale === 'zh' ? '益生菌口服液' : 'Probiotic Oral Solution',
          brand: locale === 'zh' ? '妈咪爱、合生元等' : 'Mommy\'s Love, Biostime, etc.',
          safety: 'safe',
          dosage: locale === 'zh' ? '按产品说明使用' : 'Use according to product instructions',
          notes: locale === 'zh' ? '调节肠道菌群，非常安全' : 'Regulates intestinal flora, very safe'
        }
      ]
    }
  ]

  const prohibitedIngredients = [
    {
      ingredient: locale === 'zh' ? '薄荷脑/薄荷油' : 'Menthol/Mint Oil',
      risk: 'high',
      commonIn: locale === 'zh' ? '止咳糖浆、清热口服液' : 'Cough syrups, heat-clearing oral solutions',
      reason: locale === 'zh' ? '可能引发严重溶血反应' : 'May trigger severe hemolytic reactions'
    },
    {
      ingredient: locale === 'zh' ? '樟脑' : 'Camphor',
      risk: 'high',
      commonIn: locale === 'zh' ? '某些中药口服液' : 'Some TCM oral solutions',
      reason: locale === 'zh' ? '高风险溶血成分' : 'High-risk hemolytic component'
    },
    {
      ingredient: locale === 'zh' ? '牛黄' : 'Calculus Bovis',
      risk: 'high',
      commonIn: locale === 'zh' ? '小儿清热口服液' : 'Pediatric heat-clearing oral solutions',
      reason: locale === 'zh' ? '传统禁用成分' : 'Traditional prohibited ingredient'
    },
    {
      ingredient: locale === 'zh' ? '人工色素（某些）' : 'Artificial Colors (certain)',
      risk: 'medium',
      commonIn: locale === 'zh' ? '彩色口服液' : 'Colored oral solutions',
      reason: locale === 'zh' ? '部分色素可能有风险' : 'Some dyes may pose risks'
    }
  ]

  const ageGuidelines = [
    {
      age: locale === 'zh' ? '0-6个月' : '0-6 months',
      recommendations: [
        locale === 'zh' ? '尽量避免使用口服液药物' : 'Avoid oral liquid medications when possible',
        locale === 'zh' ? '必要时只使用对乙酰氨基酚' : 'Use only acetaminophen when necessary',
        locale === 'zh' ? '严格按体重计算剂量' : 'Strictly calculate dosage by weight',
        locale === 'zh' ? '密切监测反应' : 'Monitor reactions closely'
      ]
    },
    {
      age: locale === 'zh' ? '6个月-2岁' : '6 months - 2 years',
      recommendations: [
        locale === 'zh' ? '可使用对乙酰氨基酚和布洛芬' : 'Can use acetaminophen and ibuprofen',
        locale === 'zh' ? '避免复方制剂' : 'Avoid combination preparations',
        locale === 'zh' ? '选择无糖配方' : 'Choose sugar-free formulations',
        locale === 'zh' ? '注意过敏反应' : 'Watch for allergic reactions'
      ]
    },
    {
      age: locale === 'zh' ? '2岁以上' : 'Over 2 years',
      recommendations: [
        locale === 'zh' ? '可选择的药物种类增加' : 'More medication options available',
        locale === 'zh' ? '仍需避免禁用成分' : 'Still need to avoid prohibited ingredients',
        locale === 'zh' ? '可使用止咳化痰类药物' : 'Can use cough and expectorant medications',
        locale === 'zh' ? '教育孩子配合用药' : 'Educate child to cooperate with medication'
      ]
    }
  ]

  const administrationTips = [
    {
      title: locale === 'zh' ? '正确的给药方式' : 'Proper Administration Method',
      tips: [
        locale === 'zh' ? '使用专用量杯或注射器' : 'Use dedicated measuring cup or syringe',
        locale === 'zh' ? '确保孩子完全坐立' : 'Ensure child is fully upright',
        locale === 'zh' ? '慢慢喂服，避免呛咳' : 'Administer slowly to avoid choking',
        locale === 'zh' ? '服药后多喝水' : 'Drink plenty of water after medication'
      ]
    },
    {
      title: locale === 'zh' ? '储存注意事项' : 'Storage Precautions',
      tips: [
        locale === 'zh' ? '避光、阴凉、干燥处保存' : 'Store in cool, dry, dark place',
        locale === 'zh' ? '注意有效期' : 'Check expiration date',
        locale === 'zh' ? '开封后按说明书储存' : 'Store according to instructions after opening',
        locale === 'zh' ? '放在儿童接触不到的地方' : 'Keep out of reach of children'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-pink-600 to-pink-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <HeartIcon className="h-16 w-16 mx-auto mb-6 opacity-90" />
            <h1 className="text-3xl md:text-4xl font-bold mb-6">
              {locale === 'zh' ? '儿童口服液安全指导' : 'Children\'s Oral Solution Safety Guide'}
            </h1>
            <p className="text-xl opacity-90">
              {locale === 'zh' 
                ? '为G6PD缺乏症儿童提供安全的口服液用药指导'
                : 'Safe oral solution medication guidance for children with G6PD deficiency'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Important Notice */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-blue-50 border-l-4 border-blue-400 p-6 rounded-lg">
            <div className="flex items-start">
              <InformationCircleIcon className="h-6 w-6 text-blue-600 mt-1 mr-3 flex-shrink-0" />
              <div>
                <h2 className="text-xl font-semibold text-blue-800 mb-3">
                  {locale === 'zh' ? '特别提醒' : 'Special Notice'}
                </h2>
                <p className="text-blue-700 leading-relaxed">
                  {locale === 'zh' 
                    ? '儿童G6PD缺乏症患者对药物更加敏感，任何口服液的使用都应在儿科医生指导下进行。家长应仔细阅读药品说明书，避免含有禁用成分的产品。'
                    : 'Children with G6PD deficiency are more sensitive to medications. Any oral solution use should be under pediatric supervision. Parents should carefully read medication labels and avoid products with prohibited ingredients.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Safe Oral Solutions */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '安全的儿童口服液' : 'Safe Children\'s Oral Solutions'}
          </h2>
          
          <div className="space-y-8">
            {safeOralSolutions.map((category, index) => (
              <div key={index} className="bg-green-50 rounded-lg p-6 border-l-4 border-green-500">
                <h3 className="text-xl font-semibold text-green-800 mb-6">
                  {category.category}
                </h3>
                <div className="space-y-4">
                  {category.products.map((product, productIndex) => (
                    <div key={productIndex} className="bg-white p-4 rounded-lg border">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{product.name}</h4>
                          <p className="text-gray-600 text-sm">{product.brand}</p>
                        </div>
                        <span className={`px-3 py-1 rounded text-sm font-semibold ${
                          product.safety === 'safe' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {product.safety === 'safe' 
                            ? (locale === 'zh' ? '安全' : 'Safe')
                            : (locale === 'zh' ? '谨慎' : 'Caution')
                          }
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong className="text-gray-700">{locale === 'zh' ? '用法用量：' : 'Dosage: '}</strong>
                          <span className="text-gray-600">{product.dosage}</span>
                        </div>
                        <div>
                          <strong className="text-gray-700">{locale === 'zh' ? '注意事项：' : 'Notes: '}</strong>
                          <span className="text-gray-600">{product.notes}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Prohibited Ingredients */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '需要避免的成分' : 'Ingredients to Avoid'}
          </h2>
          
          <div className="space-y-4">
            {prohibitedIngredients.map((item, index) => (
              <div key={index} className={`p-6 rounded-lg border-l-4 ${
                item.risk === 'high' 
                  ? 'bg-red-50 border-red-500' 
                  : 'bg-yellow-50 border-yellow-500'
              }`}>
                <div className="flex items-start justify-between mb-3">
                  <h3 className={`font-semibold text-lg ${
                    item.risk === 'high' ? 'text-red-800' : 'text-yellow-800'
                  }`}>
                    {item.ingredient}
                  </h3>
                  <span className={`px-3 py-1 rounded text-sm font-semibold ${
                    item.risk === 'high' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {item.risk === 'high' 
                      ? (locale === 'zh' ? '高风险' : 'High Risk')
                      : (locale === 'zh' ? '中风险' : 'Medium Risk')
                    }
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong className={`${
                      item.risk === 'high' ? 'text-red-700' : 'text-yellow-700'
                    }`}>
                      {locale === 'zh' ? '常见于：' : 'Common in: '}
                    </strong>
                    <span className={`${
                      item.risk === 'high' ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {item.commonIn}
                    </span>
                  </div>
                  <div>
                    <strong className={`${
                      item.risk === 'high' ? 'text-red-700' : 'text-yellow-700'
                    }`}>
                      {locale === 'zh' ? '风险原因：' : 'Risk reason: '}
                    </strong>
                    <span className={`${
                      item.risk === 'high' ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {item.reason}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Age Guidelines */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '不同年龄段用药指导' : 'Age-Specific Medication Guidelines'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {ageGuidelines.map((guideline, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 className="text-lg font-semibold text-blue-800 mb-4">
                  {guideline.age}
                </h3>
                <ul className="space-y-2">
                  {guideline.recommendations.map((rec, recIndex) => (
                    <li key={recIndex} className="text-blue-700 flex items-start">
                      <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <span className="text-sm">{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Administration Tips */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '给药技巧和储存' : 'Administration Tips and Storage'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {administrationTips.map((section, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-lg border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-2" />
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="text-gray-700 flex items-start">
                      <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Warning */}
      <section className="py-12 bg-red-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-red-900 mb-4">
              {locale === 'zh' ? '紧急情况处理' : 'Emergency Situations'}
            </h2>
            <p className="text-red-700 mb-6">
              {locale === 'zh' 
                ? '如果孩子在服用口服液后出现以下症状，请立即停药并就医：'
                : 'If your child shows the following symptoms after taking oral solutions, stop medication immediately and seek medical attention:'
              }
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              locale === 'zh' ? '皮肤发黄' : 'Yellow skin',
              locale === 'zh' ? '尿液变深' : 'Dark urine',
              locale === 'zh' ? '异常疲倦' : 'Unusual fatigue',
              locale === 'zh' ? '呼吸困难' : 'Breathing difficulty'
            ].map((symptom, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border-l-4 border-red-500 text-center">
                <XCircleIcon className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <span className="text-red-800 font-medium">{symptom}</span>
              </div>
            ))}
          </div>
          
          <div className="mt-8 text-center">
            <p className="text-red-800 font-semibold">
              {locale === 'zh' ? '紧急联系：120（中国大陆）' : 'Emergency Contact: Local Emergency Services'}
            </p>
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '相关信息' : 'Related Information'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href={`/${locale}/medications/oral-solutions`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '口服液用药指导' : 'Oral Solution Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解更多口服液安全使用信息' : 'Learn more about safe oral solution usage'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/medication-safety-check`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '药物安全检查' : 'Medication Safety Check'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '学习如何判断药物是否安全' : 'Learn how to determine if medications are safe'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '识别儿童溶血症状' : 'Recognize hemolysis symptoms in children'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药用药指导' : 'Western Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解西药的安全使用信息' : 'Learn about safe western medicine usage'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。儿童用药必须在儿科医生指导下进行。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Pediatric medication must be under pediatric supervision.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
