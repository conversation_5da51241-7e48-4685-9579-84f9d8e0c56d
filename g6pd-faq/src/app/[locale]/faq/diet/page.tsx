import Link from 'next/link'
import { getFaqsByCategory } from '@/lib/faq-data'

interface DietPageProps {
  params: Promise<{ locale: string }>
}

export default async function DietPage({ params }: DietPageProps) {
  const { locale } = await params
  const isZh = locale === 'zh'
  const faqs = getFaqsByCategory('diet', locale as 'zh' | 'en')

  const forbiddenFoods = {
    zh: [
      { 
        category: '豆类及制品', 
        items: ['蚕豆', '蚕豆制品', '蚕豆粉', '蚕豆淀粉'],
        risk: '严禁',
        description: '蚕豆是G6PD缺乏症患者的主要禁忌食物，可引起严重溶血反应'
      },
      { 
        category: '某些蔬菜', 
        items: ['苦瓜', '丝瓜', '冬瓜籽'],
        risk: '谨慎',
        description: '部分患者可能对这些蔬菜敏感，建议少量试用'
      },
      { 
        category: '特定水果', 
        items: ['蓝莓', '葡萄（大量）'],
        risk: '适量',
        description: '含有较高抗氧化物质，大量食用需谨慎'
      }
    ],
    en: [
      { 
        category: 'Beans and Products', 
        items: ['Fava beans', 'Fava bean products', 'Fava bean flour', 'Fava bean starch'],
        risk: 'Strictly Forbidden',
        description: 'Fava beans are the main forbidden food for G6PD deficiency patients, can cause severe hemolytic reactions'
      },
      { 
        category: 'Certain Vegetables', 
        items: ['Bitter melon', 'Luffa', 'Winter melon seeds'],
        risk: 'Caution',
        description: 'Some patients may be sensitive to these vegetables, recommend small trial amounts'
      },
      { 
        category: 'Specific Fruits', 
        items: ['Blueberries', 'Grapes (large amounts)'],
        risk: 'Moderate',
        description: 'High in antioxidants, caution needed with large consumption'
      }
    ]
  }

  const safeFoods = {
    zh: [
      {
        category: '主食类',
        items: ['大米', '小麦', '玉米', '燕麦', '小米'],
        benefits: '提供基础能量，安全无风险'
      },
      {
        category: '蛋白质',
        items: ['鸡肉', '鱼类', '鸡蛋', '牛奶', '豆腐（非蚕豆制）'],
        benefits: '优质蛋白质来源，支持身体恢复'
      },
      {
        category: '蔬菜类',
        items: ['白菜', '菠菜', '胡萝卜', '西红柿', '黄瓜'],
        benefits: '丰富维生素和矿物质，增强免疫力'
      },
      {
        category: '水果类',
        items: ['苹果', '香蕉', '橙子', '梨', '桃子'],
        benefits: '天然维生素C，促进铁吸收'
      }
    ],
    en: [
      {
        category: 'Staple Foods',
        items: ['Rice', 'Wheat', 'Corn', 'Oats', 'Millet'],
        benefits: 'Provide basic energy, safe and risk-free'
      },
      {
        category: 'Proteins',
        items: ['Chicken', 'Fish', 'Eggs', 'Milk', 'Tofu (non-fava bean)'],
        benefits: 'High-quality protein sources, support body recovery'
      },
      {
        category: 'Vegetables',
        items: ['Cabbage', 'Spinach', 'Carrots', 'Tomatoes', 'Cucumbers'],
        benefits: 'Rich in vitamins and minerals, boost immunity'
      },
      {
        category: 'Fruits',
        items: ['Apples', 'Bananas', 'Oranges', 'Pears', 'Peaches'],
        benefits: 'Natural vitamin C, promotes iron absorption'
      }
    ]
  }

  const nutritionTips = {
    zh: [
      {
        title: '补充叶酸',
        description: '多吃绿叶蔬菜、柑橘类水果，有助于红细胞生成',
        foods: ['菠菜', '西兰花', '橙子', '柠檬']
      },
      {
        title: '适量铁质',
        description: '选择易吸收的血红蛋白铁，避免过量补充',
        foods: ['瘦肉', '鱼类', '鸡蛋黄', '动物肝脏（少量）']
      },
      {
        title: '维生素E',
        description: '天然抗氧化剂，保护红细胞膜',
        foods: ['坚果', '植物油', '鳄梨', '种子类']
      },
      {
        title: '充足水分',
        description: '多喝水有助于代谢废物排出，减轻肾脏负担',
        foods: ['白开水', '淡茶', '新鲜果汁（稀释）']
      }
    ],
    en: [
      {
        title: 'Folic Acid Supplement',
        description: 'Eat more leafy greens and citrus fruits to help red blood cell production',
        foods: ['Spinach', 'Broccoli', 'Oranges', 'Lemons']
      },
      {
        title: 'Moderate Iron',
        description: 'Choose easily absorbed heme iron, avoid excessive supplementation',
        foods: ['Lean meat', 'Fish', 'Egg yolks', 'Animal liver (small amounts)']
      },
      {
        title: 'Vitamin E',
        description: 'Natural antioxidant, protects red blood cell membranes',
        foods: ['Nuts', 'Vegetable oils', 'Avocados', 'Seeds']
      },
      {
        title: 'Adequate Hydration',
        description: 'Drinking more water helps metabolic waste elimination and reduces kidney burden',
        foods: ['Plain water', 'Light tea', 'Fresh juice (diluted)']
      }
    ]
  }

  const currentForbidden = forbiddenFoods[locale as keyof typeof forbiddenFoods]
  const currentSafe = safeFoods[locale as keyof typeof safeFoods]
  const currentTips = nutritionTips[locale as keyof typeof nutritionTips]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li><Link href={`/${locale}`} className="hover:text-blue-600">{isZh ? '首页' : 'Home'}</Link></li>
            <li className="text-gray-400">/</li>
            <li><Link href={`/${locale}/faq`} className="hover:text-blue-600">{isZh ? '常见问题' : 'FAQ'}</Link></li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900">{isZh ? '饮食指导' : 'Diet Guide'}</li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="text-6xl mb-4">🥗</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {isZh ? 'G6PD缺乏症饮食指导' : 'G6PD Deficiency Diet Guide'}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {isZh 
              ? '了解G6PD缺乏症患者的饮食禁忌和营养建议，通过合理饮食维护健康'
              : 'Learn about dietary restrictions and nutritional recommendations for G6PD deficiency patients, maintain health through proper diet'
            }
          </p>
        </div>

        {/* Warning Section */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-red-100 border-l-4 border-red-500 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">⚠️</div>
              <h2 className="text-xl font-bold text-red-800">
                {isZh ? '重要提醒' : 'Important Notice'}
              </h2>
            </div>
            <p className="text-red-700">
              {isZh 
                ? '蚕豆是G6PD缺乏症患者的绝对禁忌食物！即使是少量接触也可能引起严重的溶血性贫血。请务必仔细阅读食品标签，避免含有蚕豆成分的任何食品。'
                : 'Fava beans are absolutely forbidden for G6PD deficiency patients! Even small amounts can cause severe hemolytic anemia. Please carefully read food labels and avoid any foods containing fava bean ingredients.'
              }
            </p>
          </div>
        </div>

        {/* Forbidden Foods */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '饮食禁忌' : 'Dietary Restrictions'}
          </h2>
          <div className="space-y-6">
            {currentForbidden.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-900">{category.category}</h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    category.risk === '严禁' || category.risk === 'Strictly Forbidden' 
                      ? 'bg-red-100 text-red-800' 
                      : category.risk === '谨慎' || category.risk === 'Caution'
                      ? 'bg-orange-100 text-orange-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {category.risk}
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{category.description}</p>
                <div className="flex flex-wrap gap-2">
                  {category.items.map((item, idx) => (
                    <span key={idx} className="px-3 py-1 bg-red-50 text-red-700 rounded-full text-sm">
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Safe Foods */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '安全食物' : 'Safe Foods'}
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            {currentSafe.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.category}</h3>
                <p className="text-gray-600 mb-4">{category.benefits}</p>
                <div className="flex flex-wrap gap-2">
                  {category.items.map((item, idx) => (
                    <span key={idx} className="px-3 py-1 bg-green-50 text-green-700 rounded-full text-sm">
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Nutrition Tips */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '营养建议' : 'Nutritional Recommendations'}
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            {currentTips.map((tip, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{tip.title}</h3>
                <p className="text-gray-600 mb-4">{tip.description}</p>
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-800">
                    {isZh ? '推荐食物：' : 'Recommended foods:'}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {tip.foods.map((food, idx) => (
                      <span key={idx} className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm">
                        {food}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        {faqs.length > 0 && (
          <div className="max-w-4xl mx-auto mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              {isZh ? '饮食相关常见问题' : 'Diet-Related FAQ'}
            </h2>
            <div className="space-y-6">
              {faqs.map((faq) => (
                <div key={faq.id} id={faq.id} className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {faq.question}
                  </h3>
                  <div className="text-gray-700 leading-relaxed">
                    {faq.answer.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-3 last:mb-0">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                  {faq.keywords.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex flex-wrap gap-2">
                        {faq.keywords.map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Meal Planning Tips */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-blue-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-blue-900 mb-4">
              {isZh ? '饮食规划建议' : 'Meal Planning Tips'}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">
                  {isZh ? '日常原则' : 'Daily Principles'}
                </h3>
                <ul className="text-blue-700 space-y-1">
                  <li>• {isZh ? '均衡营养，多样化饮食' : 'Balanced nutrition, diversified diet'}</li>
                  <li>• {isZh ? '少量多餐，避免暴饮暴食' : 'Small frequent meals, avoid overeating'}</li>
                  <li>• {isZh ? '充足水分，促进代谢' : 'Adequate hydration, promote metabolism'}</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">
                  {isZh ? '购物提醒' : 'Shopping Reminders'}
                </h3>
                <ul className="text-blue-700 space-y-1">
                  <li>• {isZh ? '仔细阅读食品标签' : 'Carefully read food labels'}</li>
                  <li>• {isZh ? '避免含蚕豆成分的食品' : 'Avoid foods with fava bean ingredients'}</li>
                  <li>• {isZh ? '选择新鲜天然食材' : 'Choose fresh natural ingredients'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
