import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '哪些中药成分需要避免？ - G6PD缺乏症FAQ' : 'Which Chinese Medicine Ingredients Should Be Avoided? - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细列出G6PD缺乏症患者需要避免的中药成分，包括常见中药材、中成药和复方制剂。'
      : 'Detailed list of Chinese medicine ingredients that G6PD deficient patients should avoid, including common herbs, patent medicines, and compound preparations.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,中药成分,禁用中药,中药安全,中成药'
      : 'G6PD deficiency,Chinese medicine ingredients,prohibited herbs,TCM safety,patent medicines'
  }
}

export default async function ChineseMedicineIngredientsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const prohibitedIngredients = [
    {
      category: locale === 'zh' ? '清热解毒类' : 'Heat-clearing and Detoxifying',
      herbs: [
        { name: locale === 'zh' ? '牛黄' : 'Calculus Bovis', risk: 'high', latin: 'Calculus Bovis' },
        { name: locale === 'zh' ? '珍珠' : 'Pearl', risk: 'medium', latin: 'Margarita' },
        { name: locale === 'zh' ? '雄黄' : 'Realgar', risk: 'high', latin: 'Realgar' },
        { name: locale === 'zh' ? '朱砂' : 'Cinnabar', risk: 'high', latin: 'Cinnabaris' }
      ]
    },
    {
      category: locale === 'zh' ? '理气活血类' : 'Qi-regulating and Blood-activating',
      herbs: [
        { name: locale === 'zh' ? '薄荷' : 'Mint', risk: 'medium', latin: 'Menthae Herba' },
        { name: locale === 'zh' ? '樟脑' : 'Camphor', risk: 'high', latin: 'Camphora' },
        { name: locale === 'zh' ? '冰片' : 'Borneol', risk: 'high', latin: 'Borneolum' }
      ]
    },
    {
      category: locale === 'zh' ? '解表类' : 'Exterior-releasing',
      herbs: [
        { name: locale === 'zh' ? '薄荷脑' : 'Menthol', risk: 'high', latin: 'Mentholum' },
        { name: locale === 'zh' ? '桉叶油' : 'Eucalyptus Oil', risk: 'medium', latin: 'Oleum Eucalypti' }
      ]
    }
  ]

  const commonPatentMedicines = [
    {
      name: locale === 'zh' ? '牛黄解毒片/丸' : 'Niuhuang Jiedu Tablets/Pills',
      risk: 'high',
      reason: locale === 'zh' ? '含牛黄、雄黄等高风险成分' : 'Contains high-risk ingredients like Calculus Bovis and Realgar',
      alternatives: locale === 'zh' ? '咨询医生选择安全的清热药物' : 'Consult doctor for safe heat-clearing medications'
    },
    {
      name: locale === 'zh' ? '安宫牛黄丸' : 'Angong Niuhuang Pills',
      risk: 'high',
      reason: locale === 'zh' ? '含牛黄、朱砂、雄黄' : 'Contains Calculus Bovis, Cinnabar, and Realgar',
      alternatives: locale === 'zh' ? '清开灵注射液（在医生指导下）' : 'Qingkailing Injection (under medical supervision)'
    },
    {
      name: locale === 'zh' ? '复方薄荷脑滴鼻液' : 'Compound Menthol Nasal Drops',
      risk: 'medium',
      reason: locale === 'zh' ? '含薄荷脑成分' : 'Contains menthol',
      alternatives: locale === 'zh' ? '生理盐水滴鼻液' : 'Saline nasal drops'
    },
    {
      name: locale === 'zh' ? '风油精' : 'Fengyoujing (Medicated Oil)',
      risk: 'high',
      reason: locale === 'zh' ? '含薄荷脑、樟脑等' : 'Contains menthol and camphor',
      alternatives: locale === 'zh' ? '清凉油（不含薄荷脑的）' : 'Cooling oil (menthol-free)'
    }
  ]

  const cautiousAlternatives = [
    {
      category: locale === 'zh' ? '需要谨慎的清热药' : 'Heat-clearing Herbs Requiring Caution',
      herbs: [
        { name: locale === 'zh' ? '板蓝根' : 'Isatis Root', risk: 'medium' },
        { name: locale === 'zh' ? '金银花' : 'Honeysuckle', risk: 'high' },
        { name: locale === 'zh' ? '连翘' : 'Forsythia', risk: 'high' },
        { name: locale === 'zh' ? '蒲公英' : 'Dandelion', risk: 'medium' }
      ]
    }
  ]

  const milderAlternatives = [
    {
      category: locale === 'zh' ? '相对温和的中药' : 'Relatively Milder Herbs',
      herbs: [
        { name: locale === 'zh' ? '甘草' : 'Licorice', note: locale === 'zh' ? '调和诸药，相对安全' : 'Harmonizes formulas, relatively safe' },
        { name: locale === 'zh' ? '茯苓' : 'Poria', note: locale === 'zh' ? '健脾利湿，性质平和' : 'Spleen-strengthening, mild nature' },
        { name: locale === 'zh' ? '山药' : 'Chinese Yam', note: locale === 'zh' ? '药食同源，相对安全' : 'Food-medicine dual use, relatively safe' },
        { name: locale === 'zh' ? '大枣' : 'Jujube', note: locale === 'zh' ? '补中益气，性质温和' : 'Qi-tonifying, mild nature' }
      ]
    },
    {
      category: locale === 'zh' ? '需医师指导的止咳药' : 'Cough Relief Requiring Medical Guidance',
      herbs: [
        { name: locale === 'zh' ? '川贝母' : 'Fritillaria', note: locale === 'zh' ? '润肺止咳，需医师指导' : 'Lung-moistening, requires medical guidance' },
        { name: locale === 'zh' ? '枇杷叶' : 'Loquat Leaf', note: locale === 'zh' ? '清肺止咳，需去毛处理' : 'Lung-clearing, requires proper processing' },
        { name: locale === 'zh' ? '桔梗' : 'Platycodon', note: locale === 'zh' ? '宣肺化痰，需适量使用' : 'Lung-opening, use in moderation' }
      ]
    }
  ]

  const precautions = [
    {
      title: locale === 'zh' ? '仔细阅读成分表' : 'Read Ingredient Lists Carefully',
      description: locale === 'zh' ? '购买中成药前务必查看完整成分表，避免含有禁用成分的产品' : 'Always check complete ingredient lists before purchasing patent medicines to avoid prohibited components'
    },
    {
      title: locale === 'zh' ? '咨询专业中医师' : 'Consult Professional TCM Practitioners',
      description: locale === 'zh' ? '就诊时主动告知医生您患有G6PD缺乏症，让医生选择安全的中药' : 'Actively inform TCM doctors about your G6PD deficiency so they can choose safe herbs'
    },
    {
      title: locale === 'zh' ? '避免自行购药' : 'Avoid Self-medication',
      description: locale === 'zh' ? '不要自行购买和使用中成药，特别是含有复杂成分的复方制剂' : 'Do not self-purchase and use patent medicines, especially complex compound preparations'
    },
    {
      title: locale === 'zh' ? '注意外用制剂' : 'Be Careful with Topical Preparations',
      description: locale === 'zh' ? '外用的中药制剂也可能通过皮肤吸收，同样需要注意成分安全' : 'Topical TCM preparations may also be absorbed through skin, requiring the same ingredient safety considerations'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-green-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-6">
              {locale === 'zh' ? '哪些中药成分需要避免？' : 'Which Chinese Medicine Ingredients Should Be Avoided?'}
            </h1>
            <p className="text-xl opacity-90">
              {locale === 'zh' 
                ? 'G6PD缺乏症患者需要避免的中药成分详细指南'
                : 'Detailed guide on Chinese medicine ingredients to avoid for G6PD deficient patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Quick Answer */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0" />
              <div>
                <h2 className="text-xl font-semibold text-red-800 mb-3">
                  {locale === 'zh' ? '重要提醒' : 'Important Warning'}
                </h2>
                <p className="text-red-700 leading-relaxed">
                  {locale === 'zh' 
                    ? 'G6PD缺乏症患者应避免含有牛黄、雄黄、朱砂、薄荷脑、樟脑等成分的中药。这些成分可能引发严重的溶血反应。使用任何中药前都应咨询专业中医师。'
                    : 'G6PD deficient patients should avoid Chinese medicines containing Calculus Bovis, Realgar, Cinnabar, Menthol, Camphor, and other risky ingredients. These components may trigger severe hemolytic reactions. Always consult professional TCM practitioners before using any Chinese medicine.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Prohibited Ingredients */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '禁用中药成分' : 'Prohibited Chinese Medicine Ingredients'}
          </h2>
          
          <div className="space-y-8">
            {prohibitedIngredients.map((category, index) => (
              <div key={index} className="bg-red-50 rounded-lg p-6 border-l-4 border-red-500">
                <h3 className="text-xl font-semibold text-red-800 mb-4">
                  {category.category}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {category.herbs.map((herb, herbIndex) => (
                    <div key={herbIndex} className="bg-white p-4 rounded border">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{herb.name}</h4>
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          herb.risk === 'high' 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {herb.risk === 'high' 
                            ? (locale === 'zh' ? '高风险' : 'High Risk')
                            : (locale === 'zh' ? '中风险' : 'Medium Risk')
                          }
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm italic">{herb.latin}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Common Patent Medicines */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '常见禁用中成药' : 'Common Prohibited Patent Medicines'}
          </h2>
          
          <div className="space-y-6">
            {commonPatentMedicines.map((medicine, index) => (
              <div key={index} className={`p-6 rounded-lg border-l-4 ${
                medicine.risk === 'high' 
                  ? 'bg-red-50 border-red-500' 
                  : 'bg-yellow-50 border-yellow-500'
              }`}>
                <div className="flex items-start justify-between mb-3">
                  <h3 className={`font-semibold text-lg ${
                    medicine.risk === 'high' ? 'text-red-800' : 'text-yellow-800'
                  }`}>
                    {medicine.name}
                  </h3>
                  <span className={`px-3 py-1 rounded text-sm font-semibold ${
                    medicine.risk === 'high' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {medicine.risk === 'high' 
                      ? (locale === 'zh' ? '严禁使用' : 'Strictly Prohibited')
                      : (locale === 'zh' ? '谨慎使用' : 'Use with Caution')
                    }
                  </span>
                </div>
                <p className={`mb-3 ${
                  medicine.risk === 'high' ? 'text-red-700' : 'text-yellow-700'
                }`}>
                  <strong>{locale === 'zh' ? '禁用原因：' : 'Reason: '}</strong>
                  {medicine.reason}
                </p>
                <p className="text-gray-700">
                  <strong>{locale === 'zh' ? '安全替代：' : 'Safe Alternatives: '}</strong>
                  {medicine.alternatives}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Cautious Alternatives */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '需要谨慎的中药' : 'Chinese Medicines Requiring Caution'}
          </h2>

          <div className="grid grid-cols-1 gap-6 mb-12">
            {cautiousAlternatives.map((category, index) => (
              <div key={index} className="bg-yellow-50 rounded-lg p-6 border-l-4 border-yellow-500">
                <h3 className="text-lg font-semibold text-yellow-800 mb-4 flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                  {category.category}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {category.herbs.map((herb, herbIndex) => (
                    <div key={herbIndex} className="flex items-center justify-between p-3 bg-yellow-100 rounded">
                      <span className="text-yellow-800">{herb.name}</span>
                      <span className={`px-2 py-1 rounded text-xs font-semibold ${
                        herb.risk === 'high'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-200 text-yellow-800'
                      }`}>
                        {herb.risk === 'high'
                          ? (locale === 'zh' ? '高风险' : 'High Risk')
                          : (locale === 'zh' ? '中风险' : 'Medium Risk')
                        }
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '相对温和的中药' : 'Relatively Milder Chinese Medicines'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {milderAlternatives.map((category, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                  <InformationCircleIcon className="h-5 w-5 text-blue-600 mr-2" />
                  {category.category}
                </h3>
                <ul className="space-y-3">
                  {category.herbs.map((herb, herbIndex) => (
                    <li key={herbIndex} className="text-blue-700">
                      <div className="font-medium">{herb.name}</div>
                      <div className="text-sm text-blue-600 mt-1">{herb.note}</div>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="mt-8 bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-800">
              <strong>{locale === 'zh' ? '重要提醒：' : 'Important Warning: '}</strong>
              {locale === 'zh'
                ? '金银花、连翘、板蓝根等常见清热中药含有氧化性成分，G6PD缺乏症患者应避免使用。即使是相对温和的中药，也必须在专业中医师指导下使用。'
                : 'Common heat-clearing herbs like Honeysuckle, Forsythia, and Isatis Root contain oxidative components and should be avoided by G6PD deficient patients. Even relatively mild herbs must be used under professional TCM practitioner guidance.'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Precautions */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '使用中药的注意事项' : 'Precautions for Using Chinese Medicine'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {precautions.map((item, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-lg border">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-2" />
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Signs */}
      <section className="py-12 bg-red-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-red-900 mb-4">
              {locale === 'zh' ? '紧急症状识别' : 'Emergency Symptom Recognition'}
            </h2>
            <p className="text-red-700">
              {locale === 'zh' 
                ? '如果在使用中药后出现以下症状，请立即停药并就医：'
                : 'If the following symptoms occur after using Chinese medicine, stop immediately and seek medical attention:'
              }
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              locale === 'zh' ? '皮肤或眼白发黄' : 'Yellow skin or eyes',
              locale === 'zh' ? '尿液颜色加深' : 'Dark urine',
              locale === 'zh' ? '极度疲劳乏力' : 'Extreme fatigue',
              locale === 'zh' ? '呼吸急促' : 'Shortness of breath'
            ].map((symptom, index) => (
              <div key={index} className="bg-white p-4 rounded-lg border-l-4 border-red-500">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
                  <span className="text-red-800 font-medium">{symptom}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '相关信息' : 'Related Information'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href={`/${locale}/medications/chinese-medicine`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '中药用药指导' : 'Chinese Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解更多中药安全使用信息' : 'Learn more about safe Chinese medicine usage'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/medication-safety-check`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '药物安全检查' : 'Medication Safety Check'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '学习如何判断药物是否安全' : 'Learn how to determine if medications are safe'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '识别溶血危机的早期症状' : 'Recognize early symptoms of hemolytic crisis'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药用药指导' : 'Western Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解西药的安全使用信息' : 'Learn about safe western medicine usage'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何中药使用都应咨询合格的中医师。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any Chinese medicine use should consult qualified TCM practitioners.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
