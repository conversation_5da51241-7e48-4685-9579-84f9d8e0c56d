import Link from 'next/link'
import { getFaqsByCategory } from '@/lib/faq-data'

interface TreatmentPageProps {
  params: Promise<{ locale: string }>
}

export default async function TreatmentPage({ params }: TreatmentPageProps) {
  const { locale } = await params
  const isZh = locale === 'zh'
  const faqs = getFaqsByCategory('treatment', locale as 'zh' | 'en')

  const treatmentStages = {
    zh: [
      {
        stage: '急性期治疗',
        timeframe: '溶血发作时',
        priority: 'high',
        treatments: [
          '立即停用诱发药物',
          '输液维持水电解质平衡',
          '必要时输血治疗',
          '监测肾功能',
          '对症支持治疗'
        ],
        location: '住院治疗'
      },
      {
        stage: '恢复期管理',
        timeframe: '症状缓解后',
        priority: 'medium',
        treatments: [
          '继续避免诱发因素',
          '补充叶酸和维生素',
          '定期血液检查',
          '逐步恢复活动',
          '营养支持'
        ],
        location: '门诊随访'
      },
      {
        stage: '长期预防',
        timeframe: '日常生活',
        priority: 'low',
        treatments: [
          '建立用药清单',
          '携带医疗卡片',
          '定期体检',
          '健康教育',
          '家属培训'
        ],
        location: '自我管理'
      }
    ],
    en: [
      {
        stage: 'Acute Treatment',
        timeframe: 'During hemolytic crisis',
        priority: 'high',
        treatments: [
          'Immediately stop triggering medications',
          'IV fluids for electrolyte balance',
          'Blood transfusion if necessary',
          'Monitor kidney function',
          'Symptomatic supportive care'
        ],
        location: 'Hospitalization'
      },
      {
        stage: 'Recovery Management',
        timeframe: 'After symptom relief',
        priority: 'medium',
        treatments: [
          'Continue avoiding triggers',
          'Supplement folic acid and vitamins',
          'Regular blood tests',
          'Gradual activity resumption',
          'Nutritional support'
        ],
        location: 'Outpatient follow-up'
      },
      {
        stage: 'Long-term Prevention',
        timeframe: 'Daily life',
        priority: 'low',
        treatments: [
          'Establish medication list',
          'Carry medical alert card',
          'Regular health checkups',
          'Health education',
          'Family training'
        ],
        location: 'Self-management'
      }
    ]
  }

  const medications = {
    zh: [
      {
        category: '急性期用药',
        drugs: [
          { name: '生理盐水', purpose: '维持水电解质平衡', safety: 'safe' },
          { name: '叶酸', purpose: '促进红细胞生成', safety: 'safe' },
          { name: '维生素E', purpose: '抗氧化保护', safety: 'safe' },
          { name: '输血制品', purpose: '严重贫血时使用', safety: 'conditional' }
        ]
      },
      {
        category: '支持治疗',
        drugs: [
          { name: '铁剂', purpose: '纠正缺铁性贫血', safety: 'caution' },
          { name: '维生素B12', purpose: '支持造血功能', safety: 'safe' },
          { name: '蛋白质补充剂', purpose: '营养支持', safety: 'safe' },
          { name: '益生菌', purpose: '改善肠道健康', safety: 'safe' }
        ]
      }
    ],
    en: [
      {
        category: 'Acute Phase Medications',
        drugs: [
          { name: 'Normal Saline', purpose: 'Maintain electrolyte balance', safety: 'safe' },
          { name: 'Folic Acid', purpose: 'Promote red blood cell production', safety: 'safe' },
          { name: 'Vitamin E', purpose: 'Antioxidant protection', safety: 'safe' },
          { name: 'Blood Products', purpose: 'For severe anemia', safety: 'conditional' }
        ]
      },
      {
        category: 'Supportive Treatment',
        drugs: [
          { name: 'Iron Supplements', purpose: 'Correct iron deficiency anemia', safety: 'caution' },
          { name: 'Vitamin B12', purpose: 'Support hematopoietic function', safety: 'safe' },
          { name: 'Protein Supplements', purpose: 'Nutritional support', safety: 'safe' },
          { name: 'Probiotics', purpose: 'Improve gut health', safety: 'safe' }
        ]
      }
    ]
  }

  const monitoringTests = {
    zh: [
      { test: '血常规', frequency: '急性期每日，恢复期每周', purpose: '监测血红蛋白和红细胞计数' },
      { test: '网织红细胞计数', frequency: '急性期每2-3天', purpose: '评估骨髓造血功能' },
      { test: '肝功能', frequency: '急性期每2-3天', purpose: '监测胆红素水平' },
      { test: '肾功能', frequency: '急性期每日', purpose: '评估肾脏损害程度' },
      { test: 'G6PD酶活性', frequency: '恢复期一次', purpose: '确认诊断和严重程度' },
      { test: '尿常规', frequency: '急性期每日', purpose: '监测血红蛋白尿' }
    ],
    en: [
      { test: 'Complete Blood Count', frequency: 'Daily in acute phase, weekly in recovery', purpose: 'Monitor hemoglobin and red blood cell count' },
      { test: 'Reticulocyte Count', frequency: 'Every 2-3 days in acute phase', purpose: 'Assess bone marrow hematopoietic function' },
      { test: 'Liver Function', frequency: 'Every 2-3 days in acute phase', purpose: 'Monitor bilirubin levels' },
      { test: 'Kidney Function', frequency: 'Daily in acute phase', purpose: 'Assess degree of kidney damage' },
      { test: 'G6PD Enzyme Activity', frequency: 'Once during recovery', purpose: 'Confirm diagnosis and severity' },
      { test: 'Urinalysis', frequency: 'Daily in acute phase', purpose: 'Monitor hemoglobinuria' }
    ]
  }

  const currentStages = treatmentStages[locale as keyof typeof treatmentStages]
  const currentMedications = medications[locale as keyof typeof medications]
  const currentTests = monitoringTests[locale as keyof typeof monitoringTests]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li><Link href={`/${locale}`} className="hover:text-blue-600">{isZh ? '首页' : 'Home'}</Link></li>
            <li className="text-gray-400">/</li>
            <li><Link href={`/${locale}/faq`} className="hover:text-blue-600">{isZh ? '常见问题' : 'FAQ'}</Link></li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900">{isZh ? '治疗方案' : 'Treatment Options'}</li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="text-6xl mb-4">🏥</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {isZh ? 'G6PD缺乏症治疗方案' : 'G6PD Deficiency Treatment Options'}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {isZh 
              ? '了解G6PD缺乏症的治疗原则、用药指导和长期管理策略，获得专业的医疗建议'
              : 'Learn about treatment principles, medication guidance, and long-term management strategies for G6PD deficiency, get professional medical advice'
            }
          </p>
        </div>

        {/* Medical Disclaimer */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-yellow-100 border-l-4 border-yellow-500 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">⚕️</div>
              <h2 className="text-xl font-bold text-yellow-800">
                {isZh ? '医疗免责声明' : 'Medical Disclaimer'}
              </h2>
            </div>
            <p className="text-yellow-700">
              {isZh 
                ? '本页面内容仅供教育和参考目的，不能替代专业医疗建议、诊断或治疗。任何治疗决定都应在医生指导下进行。如有紧急情况，请立即就医。'
                : 'The content on this page is for educational and reference purposes only and cannot replace professional medical advice, diagnosis, or treatment. Any treatment decisions should be made under medical supervision. In case of emergency, seek immediate medical attention.'
              }
            </p>
          </div>
        </div>

        {/* Treatment Stages */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '治疗阶段' : 'Treatment Stages'}
          </h2>
          <div className="space-y-6">
            {currentStages.map((stage, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-4 ${
                      stage.priority === 'high' ? 'bg-red-500' : 
                      stage.priority === 'medium' ? 'bg-orange-500' : 'bg-green-500'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{stage.stage}</h3>
                      <p className="text-gray-600">{stage.timeframe}</p>
                    </div>
                  </div>
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                    {stage.location}
                  </span>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">
                      {isZh ? '治疗措施：' : 'Treatment Measures:'}
                    </h4>
                    <ul className="space-y-2">
                      {stage.treatments.map((treatment, idx) => (
                        <li key={idx} className="text-gray-700 flex items-start">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                          {treatment}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Medications */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '治疗用药' : 'Treatment Medications'}
          </h2>
          <div className="space-y-6">
            {currentMedications.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{category.category}</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {category.drugs.map((drug, idx) => (
                    <div key={idx} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{drug.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          drug.safety === 'safe' ? 'bg-green-100 text-green-700' :
                          drug.safety === 'caution' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-orange-100 text-orange-700'
                        }`}>
                          {drug.safety === 'safe' ? (isZh ? '安全' : 'Safe') :
                           drug.safety === 'caution' ? (isZh ? '谨慎' : 'Caution') :
                           (isZh ? '条件性' : 'Conditional')}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm">{drug.purpose}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Monitoring Tests */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '监测检查' : 'Monitoring Tests'}
          </h2>
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {isZh ? '检查项目' : 'Test Item'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {isZh ? '检查频率' : 'Frequency'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {isZh ? '检查目的' : 'Purpose'}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentTests.map((test, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {test.test}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {test.frequency}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {test.purpose}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        {faqs.length > 0 && (
          <div className="max-w-4xl mx-auto mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              {isZh ? '治疗相关常见问题' : 'Treatment-Related FAQ'}
            </h2>
            <div className="space-y-6">
              {faqs.map((faq) => (
                <div key={faq.id} id={faq.id} className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {faq.question}
                  </h3>
                  <div className="text-gray-700 leading-relaxed">
                    {faq.answer.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-3 last:mb-0">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                  {faq.keywords.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex flex-wrap gap-2">
                        {faq.keywords.map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Emergency Guidelines */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-red-900 mb-4">
              {isZh ? '紧急处理指南' : 'Emergency Treatment Guidelines'}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-red-800 mb-2">
                  {isZh ? '立即行动' : 'Immediate Actions'}
                </h3>
                <ul className="text-red-700 space-y-1">
                  <li>• {isZh ? '停用所有可疑药物' : 'Stop all suspected medications'}</li>
                  <li>• {isZh ? '大量饮水' : 'Drink plenty of water'}</li>
                  <li>• {isZh ? '避免剧烈活动' : 'Avoid strenuous activities'}</li>
                  <li>• {isZh ? '准备就医' : 'Prepare for medical care'}</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-red-800 mb-2">
                  {isZh ? '就医信息' : 'Medical Information'}
                </h3>
                <ul className="text-red-700 space-y-1">
                  <li>• {isZh ? '告知G6PD缺乏症病史' : 'Inform about G6PD deficiency history'}</li>
                  <li>• {isZh ? '提供用药清单' : 'Provide medication list'}</li>
                  <li>• {isZh ? '描述症状时间线' : 'Describe symptom timeline'}</li>
                  <li>• {isZh ? '携带既往检查报告' : 'Bring previous test reports'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
