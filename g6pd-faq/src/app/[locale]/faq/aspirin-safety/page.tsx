import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? 'G6PD缺乏症患者可以服用阿司匹林吗？ - G6PD缺乏症FAQ' : 'Can G6PD Deficient Patients Take Aspirin? - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细解答G6PD缺乏症患者是否可以安全服用阿司匹林，包括剂量限制、风险评估和替代方案。'
      : 'Detailed answer on whether G6PD deficient patients can safely take aspirin, including dosage limits, risk assessment, and alternatives.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,阿司匹林,用药安全,剂量,风险'
      : 'G6PD deficiency,aspirin,medication safety,dosage,risk'
  }
}

export default async function AspirinSafetyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const riskFactors = [
    {
      factor: locale === 'zh' ? '高剂量使用' : 'High-dose usage',
      risk: 'high',
      description: locale === 'zh' ? '每日超过325mg的阿司匹林可能引发溶血' : 'Daily aspirin over 325mg may trigger hemolysis'
    },
    {
      factor: locale === 'zh' ? '长期服用' : 'Long-term use',
      risk: 'medium',
      description: locale === 'zh' ? '连续使用超过7天需要医生监护' : 'Continuous use over 7 days requires medical supervision'
    },
    {
      factor: locale === 'zh' ? '感染期间' : 'During infection',
      risk: 'high',
      description: locale === 'zh' ? '发热或感染时使用风险显著增加' : 'Risk significantly increases during fever or infection'
    },
    {
      factor: locale === 'zh' ? '脱水状态' : 'Dehydration',
      risk: 'medium',
      description: locale === 'zh' ? '脱水时使用阿司匹林可能加重溶血风险' : 'Using aspirin during dehydration may worsen hemolysis risk'
    }
  ]

  const alternatives = [
    {
      name: locale === 'zh' ? '对乙酰氨基酚（扑热息痛）' : 'Acetaminophen (Paracetamol)',
      safety: 'safe',
      usage: locale === 'zh' ? '解热镇痛首选，相对安全' : 'First choice for fever and pain relief, relatively safe',
      dosage: locale === 'zh' ? '成人：500-1000mg，每6-8小时一次' : 'Adults: 500-1000mg every 6-8 hours'
    },
    {
      name: locale === 'zh' ? '布洛芬（小剂量）' : 'Ibuprofen (low dose)',
      safety: 'caution',
      usage: locale === 'zh' ? '短期使用相对安全，需医生指导' : 'Relatively safe for short-term use, requires medical guidance',
      dosage: locale === 'zh' ? '成人：200-400mg，每6-8小时一次' : 'Adults: 200-400mg every 6-8 hours'
    },
    {
      name: locale === 'zh' ? '物理降温' : 'Physical cooling',
      safety: 'safe',
      usage: locale === 'zh' ? '温水擦浴、冰敷等非药物方法' : 'Non-drug methods like tepid sponging, ice packs',
      dosage: locale === 'zh' ? '根据需要使用，无剂量限制' : 'Use as needed, no dosage restrictions'
    }
  ]

  const guidelines = [
    {
      title: locale === 'zh' ? '低剂量阿司匹林（≤100mg/天）' : 'Low-dose Aspirin (≤100mg/day)',
      recommendation: 'conditional',
      details: locale === 'zh' ? '在医生严密监护下，可考虑用于心血管保护' : 'May be considered for cardiovascular protection under strict medical supervision'
    },
    {
      title: locale === 'zh' ? '中等剂量阿司匹林（100-325mg/天）' : 'Moderate-dose Aspirin (100-325mg/day)',
      recommendation: 'caution',
      details: locale === 'zh' ? '需要权衡利弊，密切监测血红蛋白水平' : 'Requires risk-benefit assessment, close monitoring of hemoglobin levels'
    },
    {
      title: locale === 'zh' ? '高剂量阿司匹林（>325mg/天）' : 'High-dose Aspirin (>325mg/day)',
      recommendation: 'avoid',
      details: locale === 'zh' ? '应避免使用，溶血风险显著增加' : 'Should be avoided, significantly increased hemolysis risk'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-6">
              {locale === 'zh' ? 'G6PD缺乏症患者可以服用阿司匹林吗？' : 'Can G6PD Deficient Patients Take Aspirin?'}
            </h1>
            <p className="text-xl opacity-90">
              {locale === 'zh' 
                ? '了解阿司匹林对G6PD缺乏症患者的风险和安全使用指导'
                : 'Understanding aspirin risks and safe usage guidelines for G6PD deficient patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Quick Answer */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 rounded-lg">
            <div className="flex items-start">
              <InformationCircleIcon className="h-6 w-6 text-yellow-600 mt-1 mr-3 flex-shrink-0" />
              <div>
                <h2 className="text-xl font-semibold text-yellow-800 mb-3">
                  {locale === 'zh' ? '简短回答' : 'Quick Answer'}
                </h2>
                <p className="text-yellow-700 leading-relaxed">
                  {locale === 'zh' 
                    ? 'G6PD缺乏症患者可以在医生指导下谨慎使用低剂量阿司匹林（≤100mg/天），但应避免高剂量或长期使用。建议优先选择对乙酰氨基酚等更安全的替代药物。'
                    : 'G6PD deficient patients can cautiously use low-dose aspirin (≤100mg/day) under medical supervision, but should avoid high doses or long-term use. Safer alternatives like acetaminophen are recommended as first choice.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Risk Factors */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '风险因素' : 'Risk Factors'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {riskFactors.map((item, index) => (
              <div key={index} className={`p-6 rounded-lg border-l-4 ${
                item.risk === 'high' 
                  ? 'bg-red-50 border-red-500' 
                  : 'bg-yellow-50 border-yellow-500'
              }`}>
                <div className="flex items-center mb-3">
                  {item.risk === 'high' ? (
                    <XCircleIcon className="h-6 w-6 text-red-600 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-2" />
                  )}
                  <h3 className={`font-semibold ${
                    item.risk === 'high' ? 'text-red-800' : 'text-yellow-800'
                  }`}>
                    {item.factor}
                  </h3>
                </div>
                <p className={`${
                  item.risk === 'high' ? 'text-red-700' : 'text-yellow-700'
                }`}>
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Usage Guidelines */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '使用指导原则' : 'Usage Guidelines'}
          </h2>
          
          <div className="space-y-6">
            {guidelines.map((guideline, index) => (
              <div key={index} className={`p-6 rounded-lg border-l-4 ${
                guideline.recommendation === 'conditional' 
                  ? 'bg-green-50 border-green-500'
                  : guideline.recommendation === 'caution'
                  ? 'bg-yellow-50 border-yellow-500'
                  : 'bg-red-50 border-red-500'
              }`}>
                <div className="flex items-start">
                  {guideline.recommendation === 'conditional' ? (
                    <CheckCircleIcon className="h-6 w-6 text-green-600 mt-1 mr-3 flex-shrink-0" />
                  ) : guideline.recommendation === 'caution' ? (
                    <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mt-1 mr-3 flex-shrink-0" />
                  ) : (
                    <XCircleIcon className="h-6 w-6 text-red-600 mt-1 mr-3 flex-shrink-0" />
                  )}
                  <div>
                    <h3 className={`font-semibold mb-2 ${
                      guideline.recommendation === 'conditional' 
                        ? 'text-green-800'
                        : guideline.recommendation === 'caution'
                        ? 'text-yellow-800'
                        : 'text-red-800'
                    }`}>
                      {guideline.title}
                    </h3>
                    <p className={`${
                      guideline.recommendation === 'conditional' 
                        ? 'text-green-700'
                        : guideline.recommendation === 'caution'
                        ? 'text-yellow-700'
                        : 'text-red-700'
                    }`}>
                      {guideline.details}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Alternatives */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '安全替代方案' : 'Safe Alternatives'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {alternatives.map((alt, index) => (
              <div key={index} className={`p-6 rounded-lg border-2 ${
                alt.safety === 'safe' 
                  ? 'border-green-200 bg-green-50'
                  : 'border-yellow-200 bg-yellow-50'
              }`}>
                <div className="flex items-center mb-3">
                  {alt.safety === 'safe' ? (
                    <CheckCircleIcon className="h-6 w-6 text-green-600 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-2" />
                  )}
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    alt.safety === 'safe' 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {alt.safety === 'safe' 
                      ? (locale === 'zh' ? '安全' : 'Safe')
                      : (locale === 'zh' ? '谨慎' : 'Caution')
                    }
                  </span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {alt.name}
                </h3>
                <p className="text-gray-700 mb-3 text-sm">
                  {alt.usage}
                </p>
                <p className="text-gray-600 text-sm">
                  <strong>{locale === 'zh' ? '用法用量：' : 'Dosage: '}</strong>
                  {alt.dosage}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">
              {locale === 'zh' ? '重要提醒' : 'Important Notes'}
            </h2>
            <ul className="space-y-3 text-blue-800">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {locale === 'zh' 
                  ? '任何阿司匹林的使用都应在医生指导下进行，并定期监测血常规'
                  : 'Any aspirin use should be under medical supervision with regular blood count monitoring'
                }
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {locale === 'zh' 
                  ? '如出现疲劳、黄疸、深色尿液等症状，应立即停药并就医'
                  : 'If symptoms like fatigue, jaundice, or dark urine occur, stop medication immediately and seek medical attention'
                }
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                {locale === 'zh' 
                  ? '儿童和老年患者需要特别谨慎，剂量应相应调整'
                  : 'Children and elderly patients require special caution with appropriate dosage adjustments'
                }
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {locale === 'zh' ? '相关信息' : 'Related Information'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药用药指导' : 'Western Medicine Guide'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解更多西药的安全使用信息' : 'Learn more about safe western medicine usage'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/medication-safety-check`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '药物安全检查' : 'Medication Safety Check'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '学习如何判断药物是否安全' : 'Learn how to determine if medications are safe'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '识别溶血危机的早期症状' : 'Recognize early symptoms of hemolytic crisis'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/treatment`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '治疗方案' : 'Treatment Options'}
              </h3>
              <p className="text-gray-600 text-sm">
                {locale === 'zh' ? '了解G6PD缺乏症的治疗方法' : 'Learn about G6PD deficiency treatment methods'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何用药决定都应咨询合格的医疗专业人员。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any medication decisions should consult qualified medical professionals.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
