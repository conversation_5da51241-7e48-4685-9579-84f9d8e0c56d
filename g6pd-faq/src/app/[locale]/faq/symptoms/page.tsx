import Link from 'next/link'
import { getFaqsByCategory } from '@/lib/faq-data'

interface SymptomsPageProps {
  params: Promise<{ locale: string }>
}

export default async function SymptomsPage({ params }: SymptomsPageProps) {
  const { locale } = await params
  const isZh = locale === 'zh'
  const faqs = getFaqsByCategory('symptoms', locale as 'zh' | 'en')

  const emergencySymptoms = {
    zh: [
      { symptom: '深色尿液', description: '尿液呈茶色、可乐色或酱油色', severity: 'high' },
      { symptom: '严重黄疸', description: '皮肤和眼白明显发黄', severity: 'high' },
      { symptom: '极度乏力', description: '无法进行日常活动', severity: 'high' },
      { symptom: '呼吸困难', description: '气短、呼吸急促', severity: 'high' },
      { symptom: '心跳加快', description: '心率明显增快', severity: 'medium' },
      { symptom: '面色苍白', description: '皮肤失去正常血色', severity: 'medium' }
    ],
    en: [
      { symptom: 'Dark Urine', description: 'Tea, cola, or soy sauce colored urine', severity: 'high' },
      { symptom: 'Severe Jaundice', description: 'Obvious yellowing of skin and eyes', severity: 'high' },
      { symptom: 'Extreme Fatigue', description: 'Unable to perform daily activities', severity: 'high' },
      { symptom: 'Breathing Difficulty', description: 'Shortness of breath, rapid breathing', severity: 'high' },
      { symptom: 'Rapid Heartbeat', description: 'Noticeably increased heart rate', severity: 'medium' },
      { symptom: 'Pallor', description: 'Loss of normal skin color', severity: 'medium' }
    ]
  }

  const warningStages = {
    zh: [
      {
        stage: '轻度症状',
        timeframe: '接触诱因后6-24小时',
        symptoms: ['轻微乏力', '食欲不振', '轻度头晕'],
        action: '停止使用可疑药物，密切观察'
      },
      {
        stage: '中度症状',
        timeframe: '接触诱因后1-3天',
        symptoms: ['明显乏力', '面色苍白', '尿液颜色加深'],
        action: '立即就医，进行血液检查'
      },
      {
        stage: '重度症状',
        timeframe: '接触诱因后数小时至1天',
        symptoms: ['严重黄疸', '深色尿液', '呼吸困难', '心跳加快'],
        action: '紧急就医，可能需要住院治疗'
      }
    ],
    en: [
      {
        stage: 'Mild Symptoms',
        timeframe: '6-24 hours after exposure',
        symptoms: ['Mild fatigue', 'Loss of appetite', 'Mild dizziness'],
        action: 'Stop suspected medication, monitor closely'
      },
      {
        stage: 'Moderate Symptoms',
        timeframe: '1-3 days after exposure',
        symptoms: ['Obvious fatigue', 'Pallor', 'Darkening urine'],
        action: 'Seek immediate medical attention, blood tests needed'
      },
      {
        stage: 'Severe Symptoms',
        timeframe: 'Hours to 1 day after exposure',
        symptoms: ['Severe jaundice', 'Dark urine', 'Breathing difficulty', 'Rapid heartbeat'],
        action: 'Emergency medical care, hospitalization may be required'
      }
    ]
  }

  const currentSymptoms = emergencySymptoms[locale as keyof typeof emergencySymptoms]
  const currentStages = warningStages[locale as keyof typeof warningStages]

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-600">
            <li><Link href={`/${locale}`} className="hover:text-blue-600">{isZh ? '首页' : 'Home'}</Link></li>
            <li className="text-gray-400">/</li>
            <li><Link href={`/${locale}/faq`} className="hover:text-blue-600">{isZh ? '常见问题' : 'FAQ'}</Link></li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900">{isZh ? '症状识别' : 'Symptom Recognition'}</li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="text-6xl mb-4">🩺</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {isZh ? 'G6PD缺乏症症状识别指南' : 'G6PD Deficiency Symptom Recognition Guide'}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {isZh 
              ? '学习识别G6PD缺乏症的症状，了解何时需要紧急医疗救助，保护您和家人的健康安全'
              : 'Learn to recognize G6PD deficiency symptoms, understand when emergency medical help is needed, and protect your family\'s health and safety'
            }
          </p>
        </div>

        {/* Emergency Alert */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-red-100 border-l-4 border-red-500 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">🚨</div>
              <h2 className="text-xl font-bold text-red-800">
                {isZh ? '紧急情况警告' : 'Emergency Warning'}
              </h2>
            </div>
            <p className="text-red-700 mb-4">
              {isZh 
                ? '如果出现以下任何症状，请立即停用可疑药物并紧急就医：'
                : 'If any of the following symptoms occur, immediately stop the suspected medication and seek emergency medical care:'
              }
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              {currentSymptoms.map((item, index) => (
                <div key={index} className={`p-4 rounded-lg ${
                  item.severity === 'high' ? 'bg-red-200' : 'bg-orange-200'
                }`}>
                  <h3 className="font-semibold text-gray-900 mb-1">{item.symptom}</h3>
                  <p className="text-sm text-gray-700">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Symptom Progression */}
        <div className="max-w-4xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {isZh ? '症状发展阶段' : 'Symptom Progression Stages'}
          </h2>
          <div className="space-y-6">
            {currentStages.map((stage, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-4 ${
                    index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-orange-500' : 'bg-red-500'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{stage.stage}</h3>
                    <p className="text-gray-600">{stage.timeframe}</p>
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {isZh ? '主要症状：' : 'Main Symptoms:'}
                    </h4>
                    <ul className="space-y-1">
                      {stage.symptoms.map((symptom, idx) => (
                        <li key={idx} className="text-gray-700 flex items-center">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                          {symptom}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {isZh ? '应对措施：' : 'Action Required:'}
                    </h4>
                    <p className="text-gray-700">{stage.action}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        {faqs.length > 0 && (
          <div className="max-w-4xl mx-auto mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              {isZh ? '症状相关常见问题' : 'Symptom-Related FAQ'}
            </h2>
            <div className="space-y-6">
              {faqs.map((faq) => (
                <div key={faq.id} id={faq.id} className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {faq.question}
                  </h3>
                  <div className="text-gray-700 leading-relaxed">
                    {faq.answer.split('\n').map((paragraph, index) => (
                      <p key={index} className="mb-3 last:mb-0">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                  {faq.keywords.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex flex-wrap gap-2">
                        {faq.keywords.map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Emergency Contact Info */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-blue-100 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-blue-900 mb-4">
              {isZh ? '紧急联系信息' : 'Emergency Contact Information'}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">
                  {isZh ? '急救电话' : 'Emergency Numbers'}
                </h3>
                <p className="text-blue-700">
                  {isZh ? '中国大陆：120' : 'China Mainland: 120'}<br />
                  {isZh ? '香港：999' : 'Hong Kong: 999'}<br />
                  {isZh ? '台湾：119' : 'Taiwan: 119'}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">
                  {isZh ? '就医提醒' : 'Medical Reminder'}
                </h3>
                <p className="text-blue-700">
                  {isZh 
                    ? '就医时请主动告知医生您患有G6PD缺乏症，并携带相关医疗记录。'
                    : 'When seeking medical care, actively inform doctors about your G6PD deficiency and bring relevant medical records.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
