import { Metadata } from 'next'

interface PrivacyPageProps {
  params: Promise<{ locale: string }>
}

export async function generateMetadata({ params }: PrivacyPageProps): Promise<Metadata> {
  const { locale } = await params
  const isZh = locale === 'zh'
  
  return {
    title: isZh ? '隐私政策 - G6PD缺乏症FAQ' : 'Privacy Policy - G6PD Deficiency FAQ',
    description: isZh 
      ? '了解我们如何收集、使用和保护您的个人信息。我们致力于保护用户隐私和数据安全。'
      : 'Learn how we collect, use, and protect your personal information. We are committed to protecting user privacy and data security.',
  }
}

export default async function PrivacyPage({ params }: PrivacyPageProps) {
  const { locale } = await params
  const isZh = locale === 'zh'

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            {isZh ? '隐私政策' : 'Privacy Policy'}
          </h1>
          
          <div className="prose prose-lg max-w-none">
            {isZh ? (
              <>
                <p className="text-gray-600 mb-6">
                  最后更新：2024年6月28日
                </p>
                
                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">1. 信息收集</h2>
                <p className="text-gray-700 mb-4">
                  我们收集的信息类型包括：
                </p>
                <ul className="list-disc pl-6 mb-6 text-gray-700">
                  <li>访问日志：包括IP地址、浏览器类型、访问时间等技术信息</li>
                  <li>使用数据：页面浏览记录、搜索查询、点击行为等</li>
                  <li>设备信息：设备类型、操作系统、屏幕分辨率等</li>
                </ul>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">2. 信息使用</h2>
                <p className="text-gray-700 mb-4">
                  我们使用收集的信息用于：
                </p>
                <ul className="list-disc pl-6 mb-6 text-gray-700">
                  <li>改善网站功能和用户体验</li>
                  <li>分析网站使用情况和趋势</li>
                  <li>提供个性化内容推荐</li>
                  <li>确保网站安全和防止滥用</li>
                </ul>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">3. 信息保护</h2>
                <p className="text-gray-700 mb-6">
                  我们采取适当的技术和组织措施来保护您的个人信息，包括加密传输、访问控制和定期安全审查。
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">4. Cookie使用</h2>
                <p className="text-gray-700 mb-6">
                  我们使用Cookie来改善用户体验，包括记住语言偏好、分析网站使用情况等。您可以通过浏览器设置管理Cookie。
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">5. 第三方服务</h2>
                <p className="text-gray-700 mb-6">
                  我们可能使用第三方分析服务（如Google Analytics）来了解网站使用情况。这些服务有自己的隐私政策。
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">6. 联系我们</h2>
                <p className="text-gray-700 mb-6">
                  如果您对本隐私政策有任何疑问，请通过以下方式联系我们：
                </p>
                <p className="text-gray-700">
                  邮箱：<EMAIL>
                </p>
              </>
            ) : (
              <>
                <p className="text-gray-600 mb-6">
                  Last updated: June 28, 2024
                </p>
                
                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">1. Information Collection</h2>
                <p className="text-gray-700 mb-4">
                  We collect the following types of information:
                </p>
                <ul className="list-disc pl-6 mb-6 text-gray-700">
                  <li>Access logs: including IP addresses, browser types, access times, and other technical information</li>
                  <li>Usage data: page views, search queries, click behavior, etc.</li>
                  <li>Device information: device type, operating system, screen resolution, etc.</li>
                </ul>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">2. Information Use</h2>
                <p className="text-gray-700 mb-4">
                  We use collected information to:
                </p>
                <ul className="list-disc pl-6 mb-6 text-gray-700">
                  <li>Improve website functionality and user experience</li>
                  <li>Analyze website usage patterns and trends</li>
                  <li>Provide personalized content recommendations</li>
                  <li>Ensure website security and prevent abuse</li>
                </ul>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">3. Information Protection</h2>
                <p className="text-gray-700 mb-6">
                  We implement appropriate technical and organizational measures to protect your personal information, including encrypted transmission, access controls, and regular security reviews.
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">4. Cookie Usage</h2>
                <p className="text-gray-700 mb-6">
                  We use cookies to improve user experience, including remembering language preferences and analyzing website usage. You can manage cookies through your browser settings.
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">5. Third-Party Services</h2>
                <p className="text-gray-700 mb-6">
                  We may use third-party analytics services (such as Google Analytics) to understand website usage. These services have their own privacy policies.
                </p>

                <h2 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">6. Contact Us</h2>
                <p className="text-gray-700 mb-6">
                  If you have any questions about this privacy policy, please contact us:
                </p>
                <p className="text-gray-700">
                  Email: <EMAIL>
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
