import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '西药用药指导 - G6PD缺乏症FAQ' : 'Western Medicine Guide - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细的G6PD缺乏症患者西药用药指导，包括禁用药物清单、安全药物推荐和用药注意事项。'
      : 'Comprehensive western medicine guide for G6PD deficiency patients, including prohibited drugs, safe medications, and usage precautions.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,西药,禁用药物,安全用药,药物清单'
      : 'G6PD deficiency,western medicine,prohibited drugs,safe medication,drug list'
  }
}

export default async function WesternMedicinePage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const prohibitedDrugs = [
    {
      category: locale === 'zh' ? '抗疟药' : 'Antimalarials',
      drugs: [
        { name: locale === 'zh' ? '伯氨喹啉' : 'Primaquine', risk: 'high' },
        { name: locale === 'zh' ? '氯喹' : 'Chloroquine', risk: 'medium' },
        { name: locale === 'zh' ? '奎宁' : 'Quinine', risk: 'medium' }
      ]
    },
    {
      category: locale === 'zh' ? '抗生素' : 'Antibiotics',
      drugs: [
        { name: locale === 'zh' ? '磺胺类药物' : 'Sulfonamides', risk: 'high' },
        { name: locale === 'zh' ? '呋喃妥因' : 'Nitrofurantoin', risk: 'high' },
        { name: locale === 'zh' ? '氯霉素' : 'Chloramphenicol', risk: 'medium' }
      ]
    },
    {
      category: locale === 'zh' ? '解热镇痛药' : 'Analgesics',
      drugs: [
        { name: locale === 'zh' ? '阿司匹林（大剂量）' : 'Aspirin (high dose)', risk: 'medium' },
        { name: locale === 'zh' ? '非那西丁' : 'Phenacetin', risk: 'high' },
        { name: locale === 'zh' ? '安替比林' : 'Antipyrine', risk: 'medium' }
      ]
    }
  ]

  const safeDrugs = [
    {
      category: locale === 'zh' ? '解热镇痛药' : 'Analgesics',
      drugs: [
        locale === 'zh' ? '对乙酰氨基酚（扑热息痛）' : 'Acetaminophen (Paracetamol)',
        locale === 'zh' ? '布洛芬（小剂量）' : 'Ibuprofen (low dose)',
        locale === 'zh' ? '双氯芬酸' : 'Diclofenac'
      ]
    },
    {
      category: locale === 'zh' ? '抗生素' : 'Antibiotics',
      drugs: [
        locale === 'zh' ? '青霉素类' : 'Penicillins',
        locale === 'zh' ? '头孢菌素类' : 'Cephalosporins',
        locale === 'zh' ? '红霉素' : 'Erythromycin'
      ]
    },
    {
      category: locale === 'zh' ? '胃肠道药物' : 'Gastrointestinal',
      drugs: [
        locale === 'zh' ? '奥美拉唑' : 'Omeprazole',
        locale === 'zh' ? '多潘立酮' : 'Domperidone',
        locale === 'zh' ? '蒙脱石散' : 'Montmorillonite'
      ]
    }
  ]

  const precautions = [
    {
      title: locale === 'zh' ? '用药前咨询' : 'Pre-medication Consultation',
      description: locale === 'zh' ? '服用任何新药前，务必告知医生您患有G6PD缺乏症' : 'Always inform your doctor about G6PD deficiency before taking any new medication',
      icon: InformationCircleIcon
    },
    {
      title: locale === 'zh' ? '剂量控制' : 'Dosage Control',
      description: locale === 'zh' ? '即使是安全药物，也应按医嘱使用，避免超量服用' : 'Even safe medications should be used as prescribed, avoid overdosing',
      icon: ShieldCheckIcon
    },
    {
      title: locale === 'zh' ? '症状监测' : 'Symptom Monitoring',
      description: locale === 'zh' ? '用药期间注意观察是否出现黄疸、尿色加深等症状' : 'Monitor for jaundice, dark urine, and other symptoms during medication',
      icon: ExclamationTriangleIcon
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-purple-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 leading-tight">
              {locale === 'zh' ? '西药用药指导' : 'Western Medicine Guide'}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl mb-6 sm:mb-8 max-w-3xl mx-auto opacity-90 leading-relaxed px-2">
              {locale === 'zh'
                ? '为G6PD缺乏症患者提供详细的西药安全用药指导'
                : 'Detailed western medicine safety guide for G6PD deficiency patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Emergency Alert */}
      <section className="bg-red-50 border-b border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-center">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-red-800">
                {locale === 'zh' ? '紧急提醒' : 'Emergency Notice'}
              </h3>
              <p className="text-red-700 mt-1">
                {locale === 'zh' 
                  ? '如出现急性溶血症状（黄疸、深色尿液、疲劳），请立即就医！'
                  : 'If acute hemolysis symptoms occur (jaundice, dark urine, fatigue), seek immediate medical attention!'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Prohibited Drugs */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '禁用药物清单' : 'Prohibited Drugs List'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? 'G6PD缺乏症患者应避免使用的西药'
                : 'Western medicines that G6PD deficient patients should avoid'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {prohibitedDrugs.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-red-500">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {category.category}
                </h3>
                <div className="space-y-3">
                  {category.drugs.map((drug, drugIndex) => (
                    <div key={drugIndex} className="flex items-center justify-between">
                      <span className="text-gray-700">{drug.name}</span>
                      <span className={`px-2 py-1 rounded text-xs font-semibold ${
                        drug.risk === 'high' 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {drug.risk === 'high' 
                          ? (locale === 'zh' ? '高风险' : 'High Risk')
                          : (locale === 'zh' ? '中风险' : 'Medium Risk')
                        }
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Safe Drugs */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相对安全药物' : 'Relatively Safe Medications'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? '在医生指导下可以使用的西药（仍需谨慎）'
                : 'Western medicines that can be used under medical supervision (still requires caution)'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {safeDrugs.map((category, index) => (
              <div key={index} className="bg-green-50 rounded-lg p-6 border-l-4 border-green-500">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600 mr-2" />
                  {category.category}
                </h3>
                <ul className="space-y-2">
                  {category.drugs.map((drug, drugIndex) => (
                    <li key={drugIndex} className="text-gray-700 flex items-center">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                      {drug}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Precautions */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '用药注意事项' : 'Medication Precautions'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {precautions.map((item, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-lg">
                <item.icon className="h-12 w-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相关资源' : 'Related Resources'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link
              href={`/${locale}/medications/chinese-medicine`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '中药指导' : 'Chinese Medicine'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '中药用药注意事项' : 'Chinese medicine precautions'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/oral-solutions`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '口服液指导' : 'Oral Solutions'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '口服液安全指导' : 'Oral solution safety guide'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '了解溶血症状' : 'Learn about hemolysis symptoms'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/treatment`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '治疗方案' : 'Treatment Options'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '治疗和管理方法' : 'Treatment and management methods'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何用药决定都应咨询合格的医疗专业人员。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any medication decisions should consult qualified medical professionals.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
