import { Metadata } from 'next'
import Link from 'next/link'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BeakerIcon
} from '@heroicons/react/24/outline'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params

  return {
    title: locale === 'zh' ? '口服液用药指导 - G6PD缺乏症FAQ' : 'Oral Solutions Guide - G6PD Deficiency FAQ',
    description: locale === 'zh' 
      ? '详细的G6PD缺乏症患者口服液用药指导，包括安全口服液推荐、禁用成分识别和儿童用药注意事项。'
      : 'Comprehensive oral solutions guide for G6PD deficiency patients, including safe oral solutions, prohibited ingredient identification, and pediatric medication precautions.',
    keywords: locale === 'zh'
      ? 'G6PD缺乏症,口服液,儿童用药,安全用药,液体药物'
      : 'G6PD deficiency,oral solutions,pediatric medication,safe medication,liquid drugs'
  }
}

export default async function OralSolutionsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params

  const safeOralSolutions = [
    {
      category: locale === 'zh' ? '退热止痛类' : 'Fever Reduction and Pain Relief',
      medications: [
        {
          name: locale === 'zh' ? '对乙酰氨基酚口服液' : 'Acetaminophen Oral Solution',
          brand: locale === 'zh' ? '泰诺林等' : 'Tylenol, etc.',
          dosage: locale === 'zh' ? '按体重计算，10-15mg/kg' : 'Calculate by weight, 10-15mg/kg',
          safety: 'safe',
          notes: locale === 'zh' ? '首选退热药物' : 'First-choice fever reducer'
        },
        {
          name: locale === 'zh' ? '布洛芬口服液' : 'Ibuprofen Oral Solution',
          brand: locale === 'zh' ? '美林等' : 'Advil, Motrin, etc.',
          dosage: locale === 'zh' ? '5-10mg/kg，6个月以上' : '5-10mg/kg, over 6 months',
          safety: 'safe',
          notes: locale === 'zh' ? '退热效果好，持续时间长' : 'Good fever reduction, long duration'
        }
      ]
    },
    {
      category: locale === 'zh' ? '止咳化痰类' : 'Cough and Expectorant',
      medications: [
        {
          name: locale === 'zh' ? '氨溴索口服液' : 'Ambroxol Oral Solution',
          brand: locale === 'zh' ? '沐舒坦等' : 'Mucosolvan, etc.',
          dosage: locale === 'zh' ? '按说明书用量' : 'According to instructions',
          safety: 'safe',
          notes: locale === 'zh' ? '化痰效果好' : 'Good expectorant effect'
        },
        {
          name: locale === 'zh' ? '右美沙芬口服液' : 'Dextromethorphan Oral Solution',
          brand: locale === 'zh' ? '惠菲宁等' : 'Robitussin DM, etc.',
          dosage: locale === 'zh' ? '按年龄和体重调整' : 'Adjust by age and weight',
          safety: 'safe',
          notes: locale === 'zh' ? '干咳首选' : 'First choice for dry cough'
        }
      ]
    },
    {
      category: locale === 'zh' ? '消化系统类' : 'Digestive System',
      medications: [
        {
          name: locale === 'zh' ? '蒙脱石散口服液' : 'Montmorillonite Oral Suspension',
          brand: locale === 'zh' ? '思密达等' : 'Smecta, etc.',
          dosage: locale === 'zh' ? '按年龄分组用量' : 'Dosage by age group',
          safety: 'safe',
          notes: locale === 'zh' ? '腹泻首选，安全性高' : 'First choice for diarrhea, high safety'
        },
        {
          name: locale === 'zh' ? '益生菌口服液' : 'Probiotic Oral Solution',
          brand: locale === 'zh' ? '妈咪爱等' : 'Mommy\'s Love, etc.',
          dosage: locale === 'zh' ? '按说明书用量' : 'According to instructions',
          safety: 'safe',
          notes: locale === 'zh' ? '调节肠道菌群' : 'Regulate intestinal flora'
        }
      ]
    }
  ]

  const dangerousIngredients = [
    {
      ingredient: locale === 'zh' ? '薄荷脑/薄荷油' : 'Menthol/Mint Oil',
      risk: 'high',
      commonIn: locale === 'zh' ? '止咳糖浆、清热口服液' : 'Cough syrups, heat-clearing oral solutions',
      alternatives: locale === 'zh' ? '选择不含薄荷的止咳药' : 'Choose menthol-free cough medicines'
    },
    {
      ingredient: locale === 'zh' ? '樟脑' : 'Camphor',
      risk: 'high',
      commonIn: locale === 'zh' ? '某些中药口服液' : 'Some TCM oral solutions',
      alternatives: locale === 'zh' ? '避免含樟脑的制剂' : 'Avoid camphor-containing preparations'
    },
    {
      ingredient: locale === 'zh' ? '牛黄' : 'Calculus Bovis',
      risk: 'high',
      commonIn: locale === 'zh' ? '清热解毒口服液' : 'Heat-clearing and detoxifying oral solutions',
      alternatives: locale === 'zh' ? '选择其他清热药物' : 'Choose other heat-clearing medications'
    },
    {
      ingredient: locale === 'zh' ? '雄黄' : 'Realgar',
      risk: 'high',
      commonIn: locale === 'zh' ? '某些中药复方制剂' : 'Some TCM compound preparations',
      alternatives: locale === 'zh' ? '避免含雄黄的制剂' : 'Avoid realgar-containing preparations'
    }
  ]

  const ageGuidelines = [
    {
      age: locale === 'zh' ? '0-6个月' : '0-6 months',
      guidelines: [
        locale === 'zh' ? '尽量避免使用口服液药物' : 'Avoid oral liquid medications when possible',
        locale === 'zh' ? '必要时选择对乙酰氨基酚' : 'Use acetaminophen when necessary',
        locale === 'zh' ? '严格按体重计算剂量' : 'Strictly calculate dosage by weight',
        locale === 'zh' ? '密切观察不良反应' : 'Closely monitor adverse reactions'
      ]
    },
    {
      age: locale === 'zh' ? '6个月-2岁' : '6 months - 2 years',
      guidelines: [
        locale === 'zh' ? '可使用对乙酰氨基酚和布洛芬' : 'Can use acetaminophen and ibuprofen',
        locale === 'zh' ? '选择无糖配方' : 'Choose sugar-free formulations',
        locale === 'zh' ? '注意药物浓度' : 'Pay attention to drug concentration',
        locale === 'zh' ? '避免含酒精的制剂' : 'Avoid alcohol-containing preparations'
      ]
    },
    {
      age: locale === 'zh' ? '2-12岁' : '2-12 years',
      guidelines: [
        locale === 'zh' ? '可使用大部分安全口服液' : 'Can use most safe oral solutions',
        locale === 'zh' ? '教育孩子配合用药' : 'Educate children to cooperate with medication',
        locale === 'zh' ? '选择口感较好的制剂' : 'Choose better-tasting preparations',
        locale === 'zh' ? '建立用药记录' : 'Establish medication records'
      ]
    }
  ]

  const administrationTips = [
    {
      title: locale === 'zh' ? '正确的给药方法' : 'Correct Administration Method',
      tips: [
        locale === 'zh' ? '使用专用量杯或注射器' : 'Use dedicated measuring cup or syringe',
        locale === 'zh' ? '确保剂量准确' : 'Ensure accurate dosage',
        locale === 'zh' ? '饭前或饭后按说明服用' : 'Take before or after meals as instructed',
        locale === 'zh' ? '不要与其他液体混合' : 'Do not mix with other liquids'
      ]
    },
    {
      title: locale === 'zh' ? '储存注意事项' : 'Storage Precautions',
      tips: [
        locale === 'zh' ? '避光、阴凉、干燥处保存' : 'Store in dark, cool, dry place',
        locale === 'zh' ? '注意保质期' : 'Pay attention to expiration date',
        locale === 'zh' ? '开封后按说明储存' : 'Store according to instructions after opening',
        locale === 'zh' ? '远离儿童接触' : 'Keep away from children'
      ]
    },
    {
      title: locale === 'zh' ? '观察要点' : 'Observation Points',
      tips: [
        locale === 'zh' ? '监测体温变化' : 'Monitor temperature changes',
        locale === 'zh' ? '观察皮肤颜色' : 'Observe skin color',
        locale === 'zh' ? '注意精神状态' : 'Pay attention to mental state',
        locale === 'zh' ? '记录用药反应' : 'Record medication reactions'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <BeakerIcon className="h-16 w-16 mx-auto mb-6 opacity-90" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {locale === 'zh' ? '口服液用药指导' : 'Oral Solutions Guide'}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
              {locale === 'zh' 
                ? '为G6PD缺乏症患者提供安全的口服液用药指导'
                : 'Safe oral solution medication guide for G6PD deficiency patients'
              }
            </p>
          </div>
        </div>
      </section>

      {/* Important Warning */}
      <section className="bg-red-50 border-b border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-4" />
            <div className="text-center">
              <h3 className="text-xl font-semibold text-red-800">
                {locale === 'zh' ? '重要提醒' : 'Important Warning'}
              </h3>
              <p className="text-red-700 mt-2">
                {locale === 'zh' 
                  ? '选择口服液时务必查看成分表，避免含有薄荷脑、樟脑、牛黄等成分的产品！'
                  : 'When choosing oral solutions, always check ingredient lists and avoid products containing menthol, camphor, Calculus Bovis, etc.!'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Safe Oral Solutions */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '安全的口服液药物' : 'Safe Oral Solution Medications'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? 'G6PD缺乏症患者可以安全使用的口服液药物'
                : 'Oral solution medications that G6PD deficient patients can safely use'
              }
            </p>
          </div>

          <div className="space-y-8">
            {safeOralSolutions.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
                  {category.category}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {category.medications.map((med, medIndex) => (
                    <div key={medIndex} className="border border-green-200 rounded-lg p-4 bg-green-50">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{med.name}</h4>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-semibold">
                          {locale === 'zh' ? '安全' : 'Safe'}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm mb-2">
                        <strong>{locale === 'zh' ? '品牌：' : 'Brand: '}</strong>{med.brand}
                      </p>
                      <p className="text-gray-600 text-sm mb-2">
                        <strong>{locale === 'zh' ? '用量：' : 'Dosage: '}</strong>{med.dosage}
                      </p>
                      <p className="text-gray-700 text-sm">
                        <strong>{locale === 'zh' ? '备注：' : 'Notes: '}</strong>{med.notes}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Dangerous Ingredients */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '需要避免的成分' : 'Ingredients to Avoid'}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' 
                ? '口服液中常见的危险成分'
                : 'Common dangerous ingredients in oral solutions'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {dangerousIngredients.map((item, index) => (
              <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-center mb-3">
                  <XCircleIcon className="h-6 w-6 text-red-600 mr-2" />
                  <h3 className="font-semibold text-red-800">{item.ingredient}</h3>
                  <span className="ml-auto px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-semibold">
                    {item.risk === 'high' ? (locale === 'zh' ? '高风险' : 'High Risk') : (locale === 'zh' ? '中风险' : 'Medium Risk')}
                  </span>
                </div>
                <p className="text-red-700 text-sm mb-2">
                  <strong>{locale === 'zh' ? '常见于：' : 'Common in: '}</strong>{item.commonIn}
                </p>
                <p className="text-red-700 text-sm">
                  <strong>{locale === 'zh' ? '替代方案：' : 'Alternatives: '}</strong>{item.alternatives}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Age Guidelines */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '年龄分组用药指导' : 'Age-Specific Medication Guidelines'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {ageGuidelines.map((group, index) => (
              <div key={index} className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 className="text-xl font-semibold text-blue-800 mb-4">
                  {group.age}
                </h3>
                <ul className="space-y-2">
                  {group.guidelines.map((guideline, guideIndex) => (
                    <li key={guideIndex} className="text-blue-700 flex items-start">
                      <InformationCircleIcon className="h-4 w-4 text-blue-600 mt-1 mr-2 flex-shrink-0" />
                      <span className="text-sm">{guideline}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Administration Tips */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '用药管理建议' : 'Medication Management Tips'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {administrationTips.map((section, index) => (
              <div key={index} className="bg-yellow-50 rounded-lg p-6 border-l-4 border-yellow-500">
                <h3 className="text-xl font-semibold text-yellow-800 mb-4">
                  {section.title}
                </h3>
                <ul className="space-y-2">
                  {section.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="text-yellow-700 flex items-start">
                      <CheckCircleIcon className="h-4 w-4 text-yellow-600 mt-1 mr-2 flex-shrink-0" />
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Links */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '相关资源' : 'Related Resources'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link
              href={`/${locale}/medications/western-medicine`}
              className="p-6 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '西药指导' : 'Western Medicine'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '西药用药注意事项' : 'Western medicine precautions'}
              </p>
            </Link>

            <Link
              href={`/${locale}/medications/chinese-medicine`}
              className="p-6 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '中药指导' : 'Chinese Medicine'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '中药用药注意事项' : 'Chinese medicine precautions'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/children-oral-solutions`}
              className="p-6 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '儿童口服液' : 'Pediatric Solutions'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '儿童专用口服液指导' : 'Pediatric oral solution guide'}
              </p>
            </Link>

            <Link
              href={`/${locale}/faq/symptoms`}
              className="p-6 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors text-center"
            >
              <h3 className="font-semibold text-gray-900 mb-2">
                {locale === 'zh' ? '症状识别' : 'Symptom Recognition'}
              </h3>
              <p className="text-sm text-gray-600">
                {locale === 'zh' ? '了解溶血症状' : 'Learn about hemolysis symptoms'}
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Medical Disclaimer */}
      <section className="py-8 bg-yellow-50 border-t border-yellow-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-yellow-800">
              <strong>{locale === 'zh' ? '医疗免责声明：' : 'Medical Disclaimer: '}</strong>
              {locale === 'zh' 
                ? '本页面信息仅供参考，不能替代专业医疗建议。任何用药决定都应咨询合格的医疗专业人员。'
                : 'The information on this page is for reference only and cannot replace professional medical advice. Any medication decisions should consult qualified medical professionals.'
              }
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
