export interface FAQ {
  id: string;
  slug: string;
  title: string;
  question: string;
  answer: string;
  shortAnswer?: string;
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
  priority: number;
  relatedFaqs: string[];
  lastUpdated: string;
  author?: string;
  medicalReview?: boolean;
  sources?: string[];
  locale: 'zh' | 'en';
}

export interface Category {
  id: string;
  slug: string;
  name: string;
  description: string;
  icon?: string;
  parentId?: string;
  children?: Category[];
  faqCount: number;
  priority: number;
  locale: 'zh' | 'en';
}

export interface Medication {
  id: string;
  name: string;
  genericName?: string;
  brandNames: string[];
  category: 'prohibited' | 'caution' | 'safe';
  type: 'chinese-medicine' | 'oral-solution' | 'western-medicine';
  description: string;
  contraindications?: string[];
  alternatives?: string[];
  dosageNotes?: string;
  relatedFaqs: string[];
  locale: 'zh' | 'en';
}

export interface SearchResult {
  id: string;
  title: string;
  excerpt: string;
  url: string;
  category: string;
  type: 'faq' | 'medication' | 'category';
  relevanceScore: number;
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  ogImage: string;
  links: {
    github?: string;
    twitter?: string;
  };
}

export interface NavigationItem {
  title: string;
  href: string;
  description?: string;
  items?: NavigationItem[];
}

export interface BreadcrumbItem {
  title: string;
  href: string;
}

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
  structuredData?: Record<string, unknown>;
}

export interface EmergencyInfo {
  title: string;
  symptoms: string[];
  actions: string[];
  whenToSeekHelp: string;
  emergencyContacts?: string[];
}

export interface DrugInteraction {
  drugName: string;
  severity: 'high' | 'medium' | 'low';
  description: string;
  alternatives?: string[];
}

export interface FaqFilter {
  category?: string;
  subcategory?: string;
  difficulty?: string;
  tags?: string[];
  search?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
