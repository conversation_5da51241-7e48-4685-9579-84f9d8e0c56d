import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

export function formatDate(date: string | Date, locale: string = 'zh'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (locale === 'zh') {
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
  
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...'
}

export function generateExcerpt(content: string, maxLength: number = 160): string {
  // Remove markdown syntax and HTML tags
  const cleanContent = content
    .replace(/#{1,6}\s+/g, '') // Remove markdown headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim()
  
  return truncateText(cleanContent, maxLength)
}

export function normalizeSearchQuery(query: string): string {
  return query
    .toLowerCase()
    .trim()
    .replace(/[^\w\s\u4e00-\u9fff]/g, '') // Keep only letters, numbers, spaces, and Chinese characters
    .replace(/\s+/g, ' ')
}

export function highlightSearchTerms(text: string, searchTerms: string[]): string {
  if (!searchTerms.length) return text
  
  const regex = new RegExp(`(${searchTerms.join('|')})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

export function calculateReadingTime(content: string, locale: string = 'zh'): number {
  // Average reading speeds (words per minute)
  const wpm = locale === 'zh' ? 300 : 200 // Chinese vs English
  
  const wordCount = locale === 'zh' 
    ? content.length // For Chinese, count characters
    : content.split(/\s+/).length // For English, count words
  
  return Math.ceil(wordCount / wpm)
}

export function generateBreadcrumbs(pathname: string, locale: string): Array<{title: string, href: string}> {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []
  
  // Remove locale from segments
  if (segments[0] === locale) {
    segments.shift()
  }
  
  // Add home
  breadcrumbs.push({
    title: locale === 'zh' ? '首页' : 'Home',
    href: `/${locale}`
  })
  
  // Add intermediate segments
  let currentPath = `/${locale}`
  for (let i = 0; i < segments.length; i++) {
    currentPath += `/${segments[i]}`
    
    // Map segment to title
    const segmentTitles: Record<string, Record<string, string>> = {
      zh: {
        faq: '常见问题',
        medications: '用药指导',
        'chinese-medicine': '中药相关',
        'oral-solutions': '口服液相关',
        'western-medicine': '西药相关',
        diet: '饮食指导',
        symptoms: '症状识别',
        treatment: '治疗方案',
        categories: '分类浏览',
        search: '搜索',
        about: '关于我们'
      },
      en: {
        faq: 'FAQ',
        medications: 'Medications',
        'chinese-medicine': 'Chinese Medicine',
        'oral-solutions': 'Oral Solutions',
        'western-medicine': 'Western Medicine',
        diet: 'Diet',
        symptoms: 'Symptoms',
        treatment: 'Treatment',
        categories: 'Categories',
        search: 'Search',
        about: 'About'
      }
    }
    
    const title = segmentTitles[locale]?.[segments[i]] || segments[i]
    breadcrumbs.push({
      title,
      href: currentPath
    })
  }
  
  return breadcrumbs
}

export function generateSEOTitle(title: string, siteName: string, locale: string): string {
  const separator = locale === 'zh' ? ' - ' : ' | '
  return `${title}${separator}${siteName}`
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export function getRelativeTimeString(date: Date | string, locale: string = 'zh'): string {
  const now = new Date()
  const targetDate = typeof date === 'string' ? new Date(date) : date
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  const intervals = {
    zh: [
      { label: '年', seconds: 31536000 },
      { label: '个月', seconds: 2592000 },
      { label: '天', seconds: 86400 },
      { label: '小时', seconds: 3600 },
      { label: '分钟', seconds: 60 },
      { label: '秒', seconds: 1 }
    ],
    en: [
      { label: 'year', seconds: 31536000 },
      { label: 'month', seconds: 2592000 },
      { label: 'day', seconds: 86400 },
      { label: 'hour', seconds: 3600 },
      { label: 'minute', seconds: 60 },
      { label: 'second', seconds: 1 }
    ]
  }
  
  for (const interval of intervals[locale as keyof typeof intervals]) {
    const count = Math.floor(diffInSeconds / interval.seconds)
    if (count > 0) {
      if (locale === 'zh') {
        return `${count}${interval.label}前`
      } else {
        return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`
      }
    }
  }
  
  return locale === 'zh' ? '刚刚' : 'just now'
}
