{"name": "g6pd-faq", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "postbuild": "next-sitemap", "deploy": "./scripts/deploy.sh", "deploy:prod": "./scripts/deploy.sh production", "analyze": "ANALYZE=true npm run build", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@heroicons/react": "^2.2.0", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "next": "15.3.4", "next-intl": "^4.3.1", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}